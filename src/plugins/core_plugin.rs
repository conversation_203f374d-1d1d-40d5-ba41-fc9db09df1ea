use crate::components::{GameAssets, GameState};
use crate::plugins::environment_plugin::EnvironmentPlugin;
use crate::plugins::genetics_plugin::GeneticsPlugin;
use crate::plugins::ik_chain_plugin::IkChainPlugin;
use crate::plugins::lifecycle_plugin::LifecyclePlugin;
use crate::plugins::perception_plugin::PerceptionPlugin;
use crate::plugins::plants_plugin::PlantsPlugin;
use crate::plugins::render_plugin::RenderPlugin;
use crate::plugins::time_of_day_plugin::TimeOfDayPlugin;
use crate::plugins::weather_plugin::WeatherPlugin;
use crate::resources::{TimeScale, WorldSimulationDeltaTime};
use crate::systems;

use crate::libraries::LibrariesPlugin;
use crate::plugins::animals::AnimalsPlugin;
use crate::plugins::blender_ik_plugin::BlenderIkIntegrationPlugin;
use crate::plugins::camera_plugin::CameraPlugin;
use crate::plugins::neural_network_plugin::{NeuralBehaviorsPlugin, NeuralNetworkPlugin};
use crate::plugins::world_generation_plugin::WorldGenerationPlugin;
use bevy::prelude::*;
use bevy_behave::prelude::BehavePlugin;
use bevy_gltf_animation::prelude::*;

pub struct CorePlugin;

impl Plugin for CorePlugin {
    fn build(&self, app: &mut App) {
        app.add_plugins(LibrariesPlugin)
            .add_plugins(PlantsPlugin)
            .add_plugins(TimeOfDayPlugin)
            .add_plugins(RenderPlugin)
            .add_plugins(GeneticsPlugin)
            .add_plugins(EnvironmentPlugin)
            .add_plugins(LifecyclePlugin)
            .add_plugins(CameraPlugin)
            .add_plugins(GltfAnimationPlugin)
            .add_plugins(WeatherPlugin)
            .add_plugins(AnimalsPlugin)
            // .add_plugins(IkChainPlugin)
            .add_plugins(PerceptionPlugin)
            .add_plugins(WorldGenerationPlugin)
            .add_plugins(BehavePlugin::default())
            .add_plugins(big_brain::BigBrainPlugin::new(PreUpdate))
            .add_plugins(NeuralNetworkPlugin)
            .add_plugins(NeuralBehaviorsPlugin)
            .init_state::<GameState>()
            .insert_resource(GameAssets::default())
            .init_resource::<TimeScale>()
            .init_resource::<WorldSimulationDeltaTime>()
            .register_type::<TimeScale>()
            .register_type::<WorldSimulationDeltaTime>()
            .register_type::<GameState>()
            .add_systems(Update, setup.run_if(in_state(GameState::Loading)))
            .add_systems(
                FixedUpdate,
                systems::core::scaled_simulation_time.run_if(in_state(GameState::Ready)),
            );
    }
}

fn setup(
    asset_server: Res<AssetServer>,
    mut game_assets: ResMut<GameAssets>,
    mut next_state: ResMut<NextState<GameState>>,
) {
    // if !asset_server
    //     .get_load_state(game_assets.0.id())
    //     .is_some_and(|s| s.is_loaded())
    // {
    //     return;
    // }

    let handle: Handle<Image> =
        asset_server.load("sprites/grass_and_flowers/leaf_2.png");
    game_assets.0 = handle;
    log::info!("Finished loading assets.");

    next_state.set(GameState::Ready);
}
