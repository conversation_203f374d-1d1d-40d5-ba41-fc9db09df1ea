//! Main IK solver systems and logic for Blender-style IK

use crate::libraries::blender_ik::constraint::BlenderIkConstraint;
use crate::libraries::blender_ik::math::*;
use crate::libraries::blender_ik::{
    <PERSON>lenderIkChain, BlenderIkJoint, BlenderIkTarget, BlenderJacobian
};
use bevy::prelude::*;
use nalgebra::{Matrix3, Vector3};
use std::collections::HashMap;

/// System to automatically add solution components to IK chains that don't have them
pub fn add_missing_solution_components(
    mut commands: Commands,
    chains_without_solution: Query<
        Entity,
        (With<BlenderIkChain>, Without<BlenderIkSolution>),
    >,
) {
    for entity in chains_without_solution.iter() {
        commands.entity(entity).insert(BlenderIkSolution::default());
    }
}

/// System that solves IK chains using <PERSON><PERSON><PERSON>'s algorithm
pub fn blender_ik_solve_system(
    mut ik_chains: Query<(&mut BlenderIkChain, &mut BlenderIkSolution)>,
    targets: Query<&BlenderIkTarget>,
    joints: Query<&BlenderIkJoint>,
    transforms: Query<&GlobalTransform>,
) {
    for (mut chain, mut solution) in ik_chains.iter_mut() {
        if !chain.enabled || chain.joints.is_empty() {
            continue;
        }

        // Get target
        let Ok(target) = targets.get(chain.target) else {
            continue;
        };

        // Solve the IK chain
        solve_ik_chain(&mut chain, target, &joints, &transforms, &mut solution);
    }
}

/// Stores computed angle updates for joints
#[derive(Component, Default, Reflect, Debug, Clone)]
#[reflect(Component)]
pub struct BlenderIkSolution {
    pub angle_updates: HashMap<Entity, Vec<f32>>,
    pub converged: bool,
    pub iterations_used: u32,
}

/// System that applies the solved IK results to transforms
pub fn blender_ik_apply_system(
    mut ik_chains: Query<(&BlenderIkChain, &mut BlenderIkSolution)>,
    joints: Query<&BlenderIkJoint>,
    mut transforms: Query<&mut Transform>,
) {
    for (chain, mut solution) in ik_chains.iter_mut() {
        if !chain.enabled || solution.angle_updates.is_empty() {
            continue;
        }

        // Apply angle updates to each joint in the chain
        for &joint_entity in &chain.joints {
            if let Some(angle_updates) = solution.angle_updates.get(&joint_entity) {
                if let (Ok(joint), Ok(mut transform)) =
                    (joints.get(joint_entity), transforms.get_mut(joint_entity))
                {
                    apply_joint_rotation_updates(
                        &joint,
                        &mut transform,
                        &joint.dof,
                        angle_updates,
                        &joint.constraints,
                    );
                }
            }
        }

        // Clear the solution after applying
        solution.angle_updates.clear();
    }
}

/// Apply rotation updates to a joint transform
fn apply_joint_rotation_updates(
    joint: &BlenderIkJoint,
    transform: &mut Transform,
    dof: &crate::libraries::blender_ik::JointDof,
    angle_updates: &[f32],
    constraints: &[BlenderIkConstraint],
) {
    // let mut update_index = 0;
    //
    // // Apply angle updates for each enabled DOF
    // if dof.rotate_x && update_index < angle_updates.len() {
    //     let update_matrix = rotation_matrix(angle_updates[update_index], 0);
    //     joint_rotation = joint_rotation * update_matrix;
    //     update_index += 1;
    // }
    //
    // if dof.rotate_y && update_index < angle_updates.len() {
    //     let update_matrix = rotation_matrix(angle_updates[update_index], 1);
    //     joint_rotation = joint_rotation * update_matrix;
    //     update_index += 1;
    // }
    //
    // if dof.rotate_z && update_index < angle_updates.len() {
    //     let update_matrix = rotation_matrix(angle_updates[update_index], 2);
    //     joint_rotation = joint_rotation * update_matrix;
    //     update_index += 1;
    // }
    // New: apply each DOF delta as a world-axis quaternion

    let mut idx = 0;
    let mut iter = angle_updates.iter();
    if dof.rotate_x {
        if let Some(&angle) = iter.next() {
            let a = joint.world_basis.col(0).normalize(); // baked X
            let delta = Quat::from_axis_angle(a, angle_updates[idx]);
            idx += 1;
            // let axis_world = transform.rotation * Vec3::X;
            // let delta = Quat::from_axis_angle(axis_world.normalize(), angle);
            transform.rotation = (delta * transform.rotation).normalize();
        }
    }

    if dof.rotate_y {
        if let Some(&angle) = iter.next() {
            let a = joint.world_basis.col(1).normalize(); // baked X
            let delta = Quat::from_axis_angle(a, angle_updates[idx]);
            idx += 1;
            // let axis_world = transform.rotation * Vec3::Y;
            // let delta = Quat::from_axis_angle(axis_world.normalize(), angle);
            transform.rotation = (delta * transform.rotation).normalize();
        }
    }

    if dof.rotate_z {
        if let Some(&angle) = iter.next() {
            let a = joint.world_basis.col(2).normalize(); // baked X
            let delta = Quat::from_axis_angle(a, angle_updates[idx]);
            idx += 1;
            // let axis_world = transform.rotation * Vec3::Z;
            // let delta = Quat::from_axis_angle(axis_world.normalize(), angle);
            transform.rotation = (delta * transform.rotation).normalize();
        }
    }

    let mut joint_rotation = quat_to_matrix3(transform.rotation);

    // Apply constraints
    for constraint in constraints {
        constraint.apply_constraint(&mut joint_rotation);
    }

    // Apply joint limits from DOF
    apply_joint_limits(&mut joint_rotation, dof);

    // Convert back to quaternion and update transform
    transform.rotation = matrix3_to_quat(joint_rotation).normalize();
}

/// Apply joint limits from DOF configuration
fn apply_joint_limits(
    joint_matrix: &mut Matrix3<f32>,
    dof: &crate::libraries::blender_ik::JointDof,
) {
    // Apply X-axis limits
    if let Some((min_x, max_x)) = dof.x_limits {
        let angle = euler_angle_from_matrix(joint_matrix, 0);
        let clamped_angle = clamp(angle, min_x, max_x);
        if (angle - clamped_angle).abs() > IK_EPSILON {
            let x_rotation = rotation_matrix(clamped_angle, 0);
            // Reconstruct matrix with clamped X rotation
            let y_angle = euler_angle_from_matrix(joint_matrix, 1);
            let z_angle = euler_angle_from_matrix(joint_matrix, 2);
            *joint_matrix =
                x_rotation * rotation_matrix(y_angle, 1) * rotation_matrix(z_angle, 2);
        }
    }

    // Apply Y-axis limits
    if let Some((min_y, max_y)) = dof.y_limits {
        let angle = euler_angle_from_matrix(joint_matrix, 1);
        let clamped_angle = clamp(angle, min_y, max_y);
        if (angle - clamped_angle).abs() > IK_EPSILON {
            let y_rotation = rotation_matrix(clamped_angle, 1);
            let x_angle = euler_angle_from_matrix(joint_matrix, 0);
            let z_angle = euler_angle_from_matrix(joint_matrix, 2);
            *joint_matrix =
                rotation_matrix(x_angle, 0) * y_rotation * rotation_matrix(z_angle, 2);
        }
    }

    // Apply Z-axis limits
    if let Some((min_z, max_z)) = dof.z_limits {
        let angle = euler_angle_from_matrix(joint_matrix, 2);
        let clamped_angle = clamp(angle, min_z, max_z);
        if (angle - clamped_angle).abs() > IK_EPSILON {
            let z_rotation = rotation_matrix(clamped_angle, 2);
            let x_angle = euler_angle_from_matrix(joint_matrix, 0);
            let y_angle = euler_angle_from_matrix(joint_matrix, 1);
            *joint_matrix =
                rotation_matrix(x_angle, 0) * rotation_matrix(y_angle, 1) * z_rotation;
        }
    }
}

/// Solve a single IK chain
fn solve_ik_chain(
    chain: &mut BlenderIkChain,
    target: &BlenderIkTarget,
    joints: &Query<&BlenderIkJoint>,
    transforms: &Query<&GlobalTransform>,
    solution: &mut BlenderIkSolution,
) {
    let joint_count = chain.joints.len();
    if joint_count == 0 {
        return;
    }

    // Count total DOF
    let mut total_dof = 0;
    let mut joint_dofs = Vec::new();

    for &joint_entity in &chain.joints {
        if let Ok(joint) = joints.get(joint_entity) {
            let dof_count = joint.dof.count();
            joint_dofs.push(dof_count);
            total_dof += dof_count;
        } else {
            joint_dofs.push(0);
        }
    }

    if total_dof == 0 {
        return;
    }

    // Initialize Jacobian solver
    let mut jacobian = BlenderJacobian::new();
    let task_size = if target.rotation.is_some() { 6 } else { 3 };
    jacobian.arm_matrices(total_dof, task_size);

    // compute max reach
    let mut max_reach = 0.0;
    let mut head_pos = Vec3::ZERO;
    for (i, &joint_ent) in chain.joints.iter().enumerate() {
        if let Ok(joint) = joints.get(joint_ent) {
            let len = joint.rest_length;
            max_reach += len;
            if i == 0 {
                head_pos = transforms.get(joint_ent).unwrap().translation();
            }
        }
    }

    // clamp the target
    let dir = target.position - head_pos;
    let dist = dir.length();
    let clamped_pos = if dist > max_reach {
        head_pos + dir.normalize() * max_reach
    } else {
        target.position
    };
    let target_pos = clamped_pos;

    // Iterative solving
    for iteration in 0..chain.max_iterations {
        // Compute current end effector position
        let end_effector_entity = chain.joints[joint_count - 1];
        let Ok(end_transform) = transforms.get(end_effector_entity) else {
            break;
        };

        // Compute current end effector (or true bone-tail) position
        let current_pos = if chain.use_tail {
            // head + local-Y * rest_length
            if let Ok(joint) = joints.get(end_effector_entity) {
                let tail_local = Vec3::Y * joint.rest_length;
                end_transform.translation() + (end_transform.rotation() * tail_local)
            } else {
                end_transform.translation()
            }
        } else {
            end_transform.translation()
        };

        // let target_pos = target.position;
        let position_error = target_pos - current_pos;

        // Check convergence
        if position_error.length() < chain.tolerance {
            break;
        }

        // Set up the Jacobian matrix
        if !setup_jacobian(&mut jacobian, chain, target, joints, transforms) {
            break;
        }

        // Set target differences (beta)
        let pos_error_nalgebra = vec3_to_vector3(position_error);
        jacobian.set_betas(0, &pos_error_nalgebra);

        // Add rotation error if needed
        if let Some(target_rotation) = target.rotation {
            if let Ok(end_transform) = transforms.get(end_effector_entity) {
                // Note: When use_tail is enabled, we still use the bone's rotation
                // since the tail doesn't have its own rotation
                let current_rotation = end_transform.rotation();
                let rotation_error =
                    compute_rotation_error(current_rotation, target_rotation);
                jacobian.set_betas(3, &rotation_error);
            }
        }

        // Solve the system
        jacobian.invert();

        // Store angle updates in solution
        store_angle_updates(chain, &jacobian, joints, solution);

        // Update convergence info
        solution.iterations_used = iteration + 1;
        solution.converged = jacobian.angle_update_norm() < chain.tolerance * 0.1;

        // Early termination if updates are very small
        if solution.converged {
            break;
        }
    }
}

/// Set up the Jacobian matrix for the current configuration
fn setup_jacobian(
    jacobian: &mut BlenderJacobian,
    chain: &BlenderIkChain,
    _target: &BlenderIkTarget,
    joints: &Query<&BlenderIkJoint>,
    transforms: &Query<&GlobalTransform>,
) -> bool {
    let joint_count = chain.joints.len();
    if joint_count == 0 {
        return false;
    }

    // Get end effector position
    let end_effector_entity = chain.joints[joint_count - 1];
    let Ok(end_transform) = transforms.get(end_effector_entity) else {
        return false;
    };

    // Compute end-pos for Jacobian (head or true tail)
    let end_pos = if chain.use_tail {
        if let Ok(joint) = joints.get(end_effector_entity) {
            let tail_local = Vec3::Y * joint.rest_length;
            let tail_world =
                end_transform.translation() + (end_transform.rotation() * tail_local);
            vec3_to_vector3(tail_world)
        } else {
            vec3_to_vector3(end_transform.translation())
        }
    } else {
        vec3_to_vector3(end_transform.translation())
    };

    let mut dof_index = 0;

    // For each joint in the chain
    for (joint_idx, &joint_entity) in chain.joints.iter().enumerate() {
        let Ok(joint) = joints.get(joint_entity) else {
            continue;
        };

        let Ok(joint_transform) = transforms.get(joint_entity) else {
            continue;
        };

        let joint_pos = vec3_to_vector3(joint_transform.translation());
        // let joint_rotation = quat_to_matrix3(joint_transform.rotation());

        // let root_pos = transforms.get(chain.joints[0]).unwrap().translation();
        // if let Some(pole_ent) = chain.pole {
        //     if let Ok(pole_tf) = transforms.get(pole_ent) {
        //         let pole_dir = (pole_tf.translation() - root_pos).normalize();
        //         // for each joint you could rotate its rest_axis a bit toward pole_dir:
        //         axis = axis.normalize()
        //             .lerp(target_dir.normalize(), chain.pole_weight)
        //             .normalize();
        //     }
        // }

        // Set DOF weight
        let weight = if joint_idx < chain.joint_weights.len() {
            chain.joint_weights[joint_idx]
        } else {
            joint.weight
        };

        let rest_basis = joint.world_basis;

        // Compute derivatives for each DOF
        if joint.dof.rotate_x {
            let axis_n: Vector3<f32> = rest_basis.col(0).into();
            let deriv_n = axis_n.cross(&(end_pos - joint_pos));
            jacobian.set_derivatives(0, dof_index, &deriv_n, 1.0);
            jacobian.set_dof_weight(dof_index, weight);
            dof_index += 1;
        }

        if joint.dof.rotate_y {
            let axis_n: Vector3<f32> = rest_basis.col(1).into();
            let deriv_n = axis_n.cross(&(end_pos - joint_pos));
            jacobian.set_derivatives(0, dof_index, &deriv_n, 1.0);
            jacobian.set_dof_weight(dof_index, weight);
            dof_index += 1;
        }

        if joint.dof.rotate_z {
            let axis_n: Vector3<f32> = rest_basis.col(2).into();
            let deriv_n = axis_n.cross(&(end_pos - joint_pos));
            jacobian.set_derivatives(0, dof_index, &deriv_n, 1.0);
            jacobian.set_dof_weight(dof_index, weight);
            dof_index += 1;
        }
    }

    true
}

/// Compute rotation error between current and target rotation
fn compute_rotation_error(current: Quat, target: Quat) -> Vector3<f32> {
    let current_matrix = quat_to_matrix3(current);
    let target_matrix = quat_to_matrix3(target);
    let error_matrix = target_matrix * current_matrix.transpose();
    matrix_to_axis_angle(&error_matrix)
}

/// Store computed angle updates in the solution
fn store_angle_updates(
    chain: &BlenderIkChain,
    jacobian: &BlenderJacobian,
    joints: &Query<&BlenderIkJoint>,
    solution: &mut BlenderIkSolution,
) {
    solution.angle_updates.clear();
    let mut dof_index = 0;

    for &joint_entity in &chain.joints {
        let Ok(joint) = joints.get(joint_entity) else {
            continue;
        };

        let mut joint_updates = Vec::new();

        // Collect angle updates for each DOF
        if joint.dof.rotate_x {
            let angle_update = if joint.locked {
                0.0
            } else {
                jacobian.angle_update(dof_index)
            };
            joint_updates.push(angle_update);
            dof_index += 1;
        }

        if joint.dof.rotate_y {
            let angle_update = if joint.locked {
                0.0
            } else {
                jacobian.angle_update(dof_index)
            };
            joint_updates.push(angle_update);
            dof_index += 1;
        }

        if joint.dof.rotate_z {
            let angle_update = if joint.locked {
                0.0
            } else {
                jacobian.angle_update(dof_index)
            };
            joint_updates.push(angle_update);
            dof_index += 1;
        }

        // Store updates for this joint
        if !joint_updates.is_empty() {
            solution.angle_updates.insert(joint_entity, joint_updates);
        }
    }
}

/// Debug system to log IK chain status
pub fn debug_ik_chains(
    chains: Query<(&BlenderIkChain, &BlenderIkSolution), Changed<BlenderIkSolution>>,
) {
    for (chain, solution) in chains.iter() {
        if solution.iterations_used > 0 {
            info!(
                "IK Chain: {} joints, {} iterations, converged: {}, updates: {}",
                chain.joints.len(),
                solution.iterations_used,
                solution.converged,
                solution.angle_updates.len()
            );
        }
    }
}

/// System to enable/disable IK chains based on conditions
pub fn manage_ik_chains(
    mut chains: Query<&mut BlenderIkChain>,
    targets: Query<&BlenderIkTarget>,
) {
    for mut chain in chains.iter_mut() {
        // Disable chain if target is missing
        if targets.get(chain.target).is_err() {
            chain.enabled = false;
        }
    }
}
