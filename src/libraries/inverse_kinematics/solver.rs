use bevy::ecs::query::QueryEntityError;
use bevy::prelude::*;
use super::{IkConstraint, IkRotationMode, IkJointConstraint};
use super::constraint::{IkSwingConstraint, IkTwistConstraint};

struct PoleTarget {
    origin: Vec3,
    tangent: Vec3,
    normal: Vec3,
    angle: f32,
}

pub(super) fn inverse_kinematics_system(
    query: Query<(Entity, &IkConstraint)>,
    parents: Query<&ChildOf>,
    mut transforms: Query<(&mut Transform, &mut GlobalTransform)>,
    swing_constraints: Query<&IkSwingConstraint>,
    twist_constraints: Query<&IkTwistConstraint>,
) {
    for (entity, constraint) in query.iter() {
        if !constraint.enabled {
            continue;
        }

        if let Err(e) = constraint.solve(
            entity, 
            &parents, 
            &mut transforms, 
            &swing_constraints,
            &twist_constraints
        ) {
            warn!("Failed to solve IK constraint: {e}");
        }
    }
}

// Add this system to your plugin
pub fn debug_ik_chain_constraint(
    mut gizmos: Gizmos,
    ik_chains: Query<(Entity, &IkConstraint)>,
    global_transforms: Query<&GlobalTransform>,
    parent_query: Query<&ChildOf>,
) {
    for (entity, chain) in ik_chains.iter() {
        // Draw target
        let target_gt = global_transforms.get(chain.target).unwrap();
        gizmos.sphere(Isometry3d::new(target_gt.translation(), Quat::IDENTITY), 0.05, Color::WHITE);

        // Draw chain
        let mut current = entity;
        let mut last_pos = if let Ok(gt) = global_transforms.get(current) {
            gt.translation()
        } else {
            continue;
        };

        // Draw a line to the target
        gizmos.line(last_pos, target_gt.translation(), Color::srgb(0.0, 1.0, 0.0));

        // Draw each bone segment
        for _ in 0..chain.chain_length {
            if let Ok(parent) = parent_query.get(current) {
                let parent_entity = parent.parent();
                if let Ok(parent_transform) = global_transforms.get(parent_entity) {
                    let parent_pos = parent_transform.translation();
                    gizmos.line(last_pos, parent_pos, Color::srgb(0.0, 1.0, 0.0));
                    last_pos = parent_pos;
                }
                current = parent_entity;
            } else {
                break;
            }
        }
    }
}

impl IkConstraint {
    pub fn solve(
        &self,
        entity: Entity,
        parents: &Query<&ChildOf>,
        transforms: &mut Query<(&mut Transform, &mut GlobalTransform)>,
        swing_constraints: &Query<&IkSwingConstraint>,
        twist_constraints: &Query<&IkTwistConstraint>,
    ) -> Result<(), QueryEntityError> {
        if self.chain_length == 0 {
            return Ok(());
        }

        let mut joints = Vec::with_capacity(self.chain_length + 2);
        joints.push(entity);

        for i in 0..self.chain_length + 1 {
            joints.push(parents.get(joints[i])?.parent());
        }

        // Get the root normal, which should have been set by the hook
        let root_normal = self.root_normal.unwrap_or(Vec3::Y);

        // Store original transforms for influence blending and to preserve rest pose
        let mut original_transforms = Vec::with_capacity(joints.len());
        let mut original_global_transforms = Vec::with_capacity(joints.len());
        for &joint in &joints {
            if let Ok((transform, global_transform)) = transforms.get(joint) {
                original_transforms.push(*transform);
                original_global_transforms.push(*global_transform);
            }
        }

        let target = transforms.get(self.target)?.1.translation();
        let normal = transforms.get(joints[0])?.0.translation;
        
        // Get target rotation if needed
        let target_rotation = match self.rotation_mode {
            IkRotationMode::None => None,
            IkRotationMode::Copy => Some(transforms.get(self.target)?.1.rotation()),
            IkRotationMode::Offset => {
                let base_rotation = transforms.get(self.target)?.1.rotation();
                Some(base_rotation * self.rotation_offset)
            }
        };
        
        // Calculate total chain length for stretch feature
        let mut total_chain_length = 0.0;
        let mut joint_lengths = Vec::with_capacity(self.chain_length);
        for i in 0..self.chain_length {
            let start = transforms.get(joints[i])?.1.translation();
            let end = transforms.get(joints[i + 1])?.1.translation();
            let length = (end - start).length();
            joint_lengths.push(length);
            total_chain_length += length;
        }

        let pole_target = if let Some(pole_target) = self.pole_target {
            let start = transforms
                .get(joints[self.chain_length])?
                .1
                .translation();
            let pole_target = transforms.get(pole_target)?.1.translation();

            let tangent = (target - start).normalize();
            let axis = (pole_target - start).cross(tangent);
            let normal = tangent.cross(axis).normalize();

            Some(PoleTarget {
                origin: start,
                tangent,
                normal,
                angle: self.pole_angle,
            })
        } else {
            None
        };

        // Store end effector's original rotation if use_tail is enabled
        let end_effector_rotation = if self.use_tail && self.rotation_mode == IkRotationMode::None {
            Some(transforms.get(joints[0])?.1.rotation())
        } else {
            None
        };

        // Calculate stretch factor if stretch is enabled
        let target_distance = (target - transforms.get(joints[self.chain_length])?.1.translation()).length();
        let stretch_scale = if self.stretch && target_distance > total_chain_length {
            (target_distance / total_chain_length).min(self.stretch_factor)
        } else {
            1.0
        };

        for _ in 0..self.iterations {
            let result = Self::solve_recursive(
                &joints[1..],
                normal,
                target,
                pole_target.as_ref(),
                transforms,
                stretch_scale,
                &joint_lengths,
                swing_constraints,
                twist_constraints,
                root_normal, // Pass the root normal
            )?;

            if result.transform_point(normal).distance_squared(target) < 0.001 {
                break;
            }
        }

        // Apply target rotation if specified
        if let Some(rotation) = target_rotation {
            let parent_rotation = transforms.get(joints[1])?.1.rotation();
            let parent_global_matrix = transforms.get(joints[1])?.1.compute_matrix();
            let parent_inv_rotation = parent_rotation.inverse();
            
            if let Ok((mut transform, mut global_transform)) = transforms.get_mut(joints[0]) {
                // Apply target rotation in world space
                transform.rotation = parent_inv_rotation * rotation;
                
                // Manually update the global transform
                let local_matrix = Mat4::from_rotation_translation(
                    transform.rotation,
                    transform.translation
                );
                *global_transform = GlobalTransform::from(parent_global_matrix * local_matrix);
            }
        }
        // Otherwise restore end effector rotation if use_tail is enabled
        else if let Some(rotation) = end_effector_rotation {
            if self.use_tail {
                // First get the parent global transform and extract what we need
                let parent_rotation = transforms.get(joints[1])?.1.rotation();
                let parent_global_matrix = transforms.get(joints[1])?.1.compute_matrix();
                let parent_inv_rotation = parent_rotation.inverse();
                
                // Then get and modify the end effector transform
                if let Ok((mut transform, mut global_transform)) = transforms.get_mut(joints[0]) {
                    // Keep the position from IK solve but restore original rotation
                    transform.rotation = parent_inv_rotation * rotation;
                    
                    // Manually update the global transform
                    let local_matrix = Mat4::from_rotation_translation(
                        transform.rotation,
                        transform.translation
                    );
                    *global_transform = GlobalTransform::from(parent_global_matrix * local_matrix);
                }
            }
        }

        // When applying the IK solution, blend with original transforms
        // This ensures we're starting from the rest pose, not a linear chain
        for i in 0..joints.len().min(original_transforms.len()) {
            if let Ok((mut transform, mut global_transform)) = transforms.get_mut(joints[i]) {
                // Apply influence blending with original transform
                if self.influence < 1.0 {
                    let orig = &original_transforms[i];
                    transform.translation = orig.translation.lerp(transform.translation, self.influence);
                    transform.rotation = orig.rotation.slerp(transform.rotation, self.influence);
                }
                
                // Update global transform if needed
                if i > 0 {
                    // if let Ok(parent_global) = transforms.get(joints[i+1]).map(|(_, gt)| *gt) {
                    //     *transform = parent_global.mul_transform(*transform).compute_transform();
                    // }
                }
            }
        }

        Ok(())
    }

    fn solve_recursive(
        chain: &[Entity],
        normal: Vec3,
        target: Vec3,
        pole_target: Option<&PoleTarget>,
        transforms: &mut Query<(&mut Transform, &mut GlobalTransform)>,
        stretch_scale: f32,
        joint_lengths: &[f32],
        swing_constraints: &Query<&IkSwingConstraint>,
        twist_constraints: &Query<&IkTwistConstraint>,
        root_normal: Vec3, // Add root_normal parameter
    ) -> Result<GlobalTransform, QueryEntityError> {
        // Get the current transform without modifying it yet
        let (transform, global_transform) = transforms.get(chain[0])?;
        let transform = *transform;
        let global_transform = *global_transform;

        if chain.len() == 1 {
            return Ok(global_transform);
        }

        // Use the actual bone direction from the transform, not assuming a linear chain
        let parent_normal = transform.translation;
        
        // determine absolute rotation and translation for this bone where the tail touches the
        // target.
        let rotation = if let Some(pt) = pole_target {
            let on_pole = pt.origin
                + (global_transform.translation() - pt.origin)
                    .project_onto_normalized(pt.tangent);
            let distance = on_pole.distance(global_transform.translation());
            let from_position = on_pole + pt.normal * distance;

            // Use root_normal to help establish the initial orientation
            let base = Quat::from_rotation_arc(root_normal.normalize(), Vec3::Z);

            let forward = (target - from_position)
                .try_normalize()
                .unwrap_or(pt.tangent);
            let up = forward.cross(pt.normal).normalize();
            let right = up.cross(forward);
            let orientation =
                Mat3::from_cols(right, up, forward) * Mat3::from_rotation_z(pt.angle);

            (Quat::from_mat3(&orientation) * base).normalize()
        } else {
            // Use root_normal to help establish the initial orientation
            let base_normal = if chain.len() == chain.len() {
                // For the first joint in the chain, use the root normal
                root_normal
            } else {
                // For other joints, use the current normal
                normal
            };
            
            Quat::from_rotation_arc(
                base_normal.normalize(),
                (target - global_transform.translation()).normalize(),
            )
            .normalize()
        };
        
        // Apply stretch scaling to the bone length
        let chain_idx = chain.len() - 1;
        let scaled_length = if chain_idx < joint_lengths.len() {
            joint_lengths[chain_idx] * stretch_scale
        } else {
            normal.length()
        };
        
        let scaled_normal = normal.normalize() * scaled_length;
        let translation = target - rotation.mul_vec3(scaled_normal);

        // recurse to target the parent towards the current translation
        let parent_global_transform = Self::solve_recursive(
            &chain[1..],
            parent_normal,
            translation,
            pole_target,
            transforms,
            stretch_scale,
            joint_lengths,
            swing_constraints,
            twist_constraints,
            root_normal, // Pass the root normal
        )?;

        // apply constraints on the way back from recursing
        let (mut transform, mut global_transform) = transforms.get_mut(chain[0])?;
        let mut new_rotation = Quat::from_affine3(&parent_global_transform.affine())
            .inverse()
            .normalize()
            * rotation;
            
        // Apply constraints if they exist
        if let Ok(swing_constraint) = swing_constraints.get(chain[0]) {
            new_rotation = swing_constraint.apply(new_rotation);
        }
        
        if let Ok(twist_constraint) = twist_constraints.get(chain[0]) {
            new_rotation = twist_constraint.apply(new_rotation);
        }
        
        transform.rotation = new_rotation;
        *global_transform = parent_global_transform.mul_transform(*transform);

        Ok(*global_transform)
    }
}
