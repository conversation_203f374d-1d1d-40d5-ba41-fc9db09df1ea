use crate::components::animals::deer::DeerQuadrupedBaseArmature;
use crate::components::animals::AnimalBaseArmature;
use crate::libraries::ik_fabrik::chain::Ik<PERSON>hain;
use crate::resources::WorldSimulationDeltaTime;
use crate::systems::environment::system_params::TerrainSystemParams;
use avian3d::prelude::*;
use bevy::ecs::component::HookContext;
use bevy::ecs::world::DeferredWorld;
use bevy::prelude::*;
use k::nalgebra::{Quaternion, Rotation3};
use k::prelude::*;
use k::{
    Chain, JacobianIkSolver, JointType, NodeBuilder, Translation3, UnitQuaternion, Vector3
};
use nalgebra as na;

pub mod gait_patterns;
pub mod ground_detection;

/// Identifies which leg this is for a quadruped
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Reflect, Component)]
pub enum LegType {
    FrontLeft,
    FrontRight,
    BackLeft,
    BackRight,
}

impl LegType {
    pub fn all() -> [LegType; 4] {
        [
            LegType::FrontLeft,
            LegType::FrontRight,
            LegType::BackLeft,
            LegType::BackRight,
        ]
    }

    pub fn is_front(&self) -> bool {
        matches!(self, LegType::FrontLeft | LegType::FrontRight)
    }

    pub fn is_left(&self) -> bool {
        matches!(self, LegType::FrontLeft | LegType::BackLeft)
    }

    pub fn diagonal_pair(&self) -> LegType {
        match self {
            LegType::FrontLeft => LegType::BackRight,
            LegType::FrontRight => LegType::BackLeft,
            LegType::BackLeft => LegType::FrontRight,
            LegType::BackRight => LegType::FrontLeft,
        }
    }
}

/// State of an individual foot during walking cycle
#[derive(Debug, Clone, Copy, PartialEq, Reflect, Component)]
pub enum FootState {
    /// Foot is on the ground, supporting weight
    Planted { time_planted: f32 },
    /// Foot is lifting off the ground
    Lifting { progress: f32 },
    /// Foot is moving through the air to new position
    Swinging { progress: f32, target_pos: Vec3 },
    /// Foot is placing down at target position
    Placing { progress: f32, target_pos: Vec3 },
}

impl Default for FootState {
    fn default() -> Self {
        FootState::Planted { time_planted: 0.0 }
    }
}

/// IK chain data for a single foot using the k crate
#[derive(Component, Reflect)]
#[reflect(Component)]
#[component(on_insert = ik_chain_insert_hook)]
pub struct FootIkChain {
    pub leg_type: LegType,
    pub foot_entity: Entity,
    pub target_entity: Entity,
    pub current_target: Vec3,
    pub rest_position: Vec3,
    pub step_height: f32,
    pub max_reach: f32,
    pub foot_state: FootState,
    pub joint_count: usize,
    /// The k crate chain for this leg
    #[reflect(ignore)]
    pub k_chain: Option<k::Chain<f32>>,
    /// The k crate serial chain for IK solving
    #[reflect(ignore)]
    pub k_serial_chain: Option<k::SerialChain<f32>>,
    /// The k crate IK solver
    #[reflect(ignore)]
    pub k_solver: k::JacobianIkSolver<f32>,
    k_chain_tail_node_name: String,
    tracked_bones: Vec<(String, Entity)>,
}

impl Default for FootIkChain {
    fn default() -> Self {
        Self {
            leg_type: LegType::FrontLeft,
            foot_entity: Entity::PLACEHOLDER,
            target_entity: Entity::PLACEHOLDER,
            current_target: Vec3::ZERO,
            rest_position: Vec3::ZERO,
            step_height: 0.3,
            max_reach: 1.5,
            foot_state: FootState::default(),
            joint_count: 4,
            k_chain: None,
            k_serial_chain: None,
            k_solver: k::JacobianIkSolver::default(),
            k_chain_tail_node_name: "".to_string(),
            tracked_bones: Vec::new(),
        }
    }
}

fn ik_chain_insert_hook3(
    mut world: DeferredWorld,
    HookContext { entity, .. }: HookContext,
) {
    let foot_chain = world.get::<FootIkChain>(entity).unwrap();

    // Collect bone entities from foot up through joint_count
    let mut bone_entities = Vec::with_capacity(foot_chain.joint_count);
    let mut current = foot_chain.foot_entity;
    for _ in 0..foot_chain.joint_count {
        let name = world.get::<Name>(current).unwrap().as_str().to_string();
        bone_entities.push((name, current));
        current = world.get::<ChildOf>(current).unwrap().parent();
    }
    // After the loop, 'current' is the parent of the last bone (e.g. shoulder)
    let parent_entity = current;
    let parent_gt = world.get::<Transform>(parent_entity).unwrap();

    // Build Node list: root at shoulder, then DOF nodes for each bone in reverse order
    let mut nodes = Vec::with_capacity(1 + foot_chain.joint_count * 3);
    let root_node = NodeBuilder::new()
        .name("fixed")
        .joint_type(JointType::Fixed)
        .translation(Translation3::new(
            parent_gt.translation.x,
            parent_gt.translation.y,
            parent_gt.translation.z,
        ))
        .into_node();
    nodes.push(root_node.clone());

    // Create 3-DOF nodes per bone (thigh -> shin -> foot)
    for (_, bone) in bone_entities.iter().rev() {
        let name = world.get::<Name>(*bone).unwrap().as_str().to_string();
        let gt = world.get::<Transform>(*bone).unwrap();
        let t3 =
            Translation3::new(gt.translation.x, gt.translation.y, gt.translation.z);
        let rot = gt.rotation; // Vec4 { x, y, z, w }
        let uq: UnitQuaternion<f32> =
            UnitQuaternion::from_quaternion(Quaternion::new(rot.w, rot.x, rot.y, rot.z));

        // Roll (X-axis)
        nodes.push(
            NodeBuilder::new()
                .name(&format!("{}_roll", name))
                .translation(t3)
                .rotation(uq)
                .joint_type(JointType::Rotational {
                    axis: Vector3::x_axis(),
                })
                .into_node(),
        );
        // Pitch (Y-axis)
        nodes.push(
            NodeBuilder::new()
                .name(&format!("{}_pitch", name))
                .translation(t3)
                .rotation(uq)
                .joint_type(JointType::Rotational {
                    axis: Vector3::y_axis(),
                })
                .into_node(),
        );
        // Yaw (Z-axis)
        nodes.push(
            NodeBuilder::new()
                .name(&format!("{}_yaw", name))
                .translation(t3)
                .rotation(uq)
                .joint_type(JointType::Rotational {
                    axis: Vector3::z_axis(),
                })
                .into_node(),
        );
    }

    // nodes.reverse();

    // Parent each node to its predecessor, making 'fixed' the parent of the first DOF
    for i in 1..nodes.len() {
        nodes[i].set_parent(&nodes[i - 1]);
    }

    // Build and initialize the k crate chain
    let chain = Chain::from_nodes(nodes.clone());
    // chain.update_transforms();

    // Create a serial chain from the end-effector node
    let tail_name = nodes.last().unwrap().joint().name.clone();
    let tail_node = chain.find(&tail_name).unwrap();
    let serial = k::SerialChain::from_end(tail_node);
    serial.update_transforms();

    // Configure solver with tighter tolerances, damping, and nullspace bias
    let mut solver = JacobianIkSolver::new(
        0.005, // allowable_target_distance
        0.005, // allowable_target_angle
        0.1,   // jacobian_multiplier (damping)
        50,    // num_max_try
    );
    // solver.set_nullspace_function(Box::new(
    //     k::create_reference_positions_nullspace_function(
    //         serial.joint_positions().to_vec(),
    //         vec![0.1; serial.joint_positions().len()],
    //     ),
    // ));

    // Store into the component
    let mut comp = world.get_mut::<FootIkChain>(entity).unwrap();
    comp.k_chain = Some(chain);
    comp.k_serial_chain = Some(serial);
    comp.k_solver = solver;
    comp.tracked_bones = bone_entities.clone();

    log::info!("tracked_bones bottom→top: {:?}", bone_entities);
    bone_entities.reverse();
    log::info!("using joint order top→bottom: {:?}", bone_entities);

    log::info!("Tail Node Name: {:?}", tail_name);
    comp.k_chain_tail_node_name = tail_name;
}

fn ik_chain_insert_hook(
    mut world: DeferredWorld,
    HookContext { entity, .. }: HookContext,
) {
    let foot_chain = world.get::<FootIkChain>(entity).unwrap();
    let joint_count = foot_chain.joint_count;
    let mut last_dir = Vec3::ZERO;
    let mut tail = foot_chain.foot_entity;

    let mut joints: Vec<k::Node<f32>> = Vec::with_capacity(joint_count);

    let fixed: k::Node<f32> = NodeBuilder::new()
        .name("fixed")
        .joint_type(JointType::Fixed)
        .translation(Translation3::new(0.0, 0.0, 0.0))
        .finalize()
        .into();

    joints.push(fixed);

    for i in 1..joint_count {
        let name = world.get::<Name>(tail).unwrap().clone();
        // log::info!("Joint Name: {:?}", name);

        // Parent entity:
        let parent = world
            .get::<ChildOf>(tail)
            .expect("Missing ChildOf on IK chain joint")
            .parent();

        let t_pos = world
            .get::<GlobalTransform>(tail)
            .expect("Missing GlobalTransform")
            .translation();

        let t_rot = world
            .get::<GlobalTransform>(tail)
            .expect("Missing GlobalTransform")
            .rotation();

        let mut builder = NodeBuilder::new();
        builder = builder.name(name.as_str());

        // let point = t_rot.to_scaled_axis();
        // let rot_axis = Vector3::new(point.x, point.y, point.z);
        // let axis = Vector3::y_axis();

        let translation = Translation3::new(t_pos.x, t_pos.y, t_pos.z);
        joints.push(
            builder
                .clone()
                .name(format!("{}_{}", name.as_str(), "roll").as_str())
                .translation(translation.clone())
                // .rotation(UnitQuaternion::from_axis_angle(&axis, t_rot.w))
                .joint_type(JointType::Rotational {
                    axis: Vector3::x_axis(),
                })
                .finalize()
                .into(),
        );

        joints.push(
            builder
                .clone()
                .name(format!("{}_{}", name.as_str(), "pitch").as_str())
                .translation(translation.clone())
                .joint_type(JointType::Rotational {
                    axis: Vector3::y_axis(),
                })
                .finalize()
                .into(),
        );

        joints.push(
            builder
                .clone()
                .name(format!("{}_{}", name.as_str(), "yaw").as_str())
                .translation(translation.clone())
                .joint_type(JointType::Rotational {
                    axis: Vector3::z_axis(),
                })
                .finalize()
                .into(),
        );
        // Check for last joint
        if i == joint_count - 1 {
            // Get parent
        }

        // let dir = t_pos - p_pos;
        // last_dir = dir;
        tail = parent;
    }

    joints.reverse();
    let mut last_node_name = "".to_string();

    // Set each node's parent
    for i in 1..joints.len() {
        let joint_1 = &joints[i];
        log::info!("Joint Name: {:?}", joint_1.joint().name);

        let joint = &joints[i - 1];
        joints[i].set_parent(joint);
        last_node_name = joint_1.joint().name.clone();
    }
    log::info!("---------------------------------");

    let binding = joints.clone();
    let root_node = binding.first().unwrap();
    log::info!("Root Node: {:?}", root_node.joint().name);

    let k_chain = Chain::from_nodes(joints);
    // log::info!("Chain: {:?}", k_chain);
    k_chain.update_transforms();

    // let leg = k::SerialChain::new_unchecked(Chain::from_root(root_node.clone()));
    let leg = k::SerialChain::from_end(root_node);
    leg.update_transforms();

    let mut chain = world.get_mut::<FootIkChain>(entity).unwrap();
    chain.k_chain = Some(k_chain);
    chain.k_serial_chain = Some(leg);
    chain.k_solver = k::JacobianIkSolver::default();
    chain.k_chain_tail_node_name = last_node_name;
}

/// Gait controller for managing walking patterns
#[derive(Component, Reflect)]
pub struct GaitController {
    pub gait_speed: f32,
    pub step_length: f32,
    pub step_height: f32,
    pub step_duration: f32,
    pub body_height: f32,
    pub stability_margin: f32,
    pub current_phase: f32,
    pub is_moving: bool,
}

impl Default for GaitController {
    fn default() -> Self {
        Self {
            gait_speed: 1.0,
            step_length: 0.8,
            step_height: 0.2,
            step_duration: 0.5,
            body_height: 1.0,
            stability_margin: 0.1,
            current_phase: 0.0,
            is_moving: false,
        }
    }
}

/// Main controller for quadruped IK system
#[derive(Component, Reflect)]
pub struct QuadrupedIkController {
    pub legs: [Entity; 4], // [FrontLeft, FrontRight, BackLeft, BackRight]
    pub body_entity: Entity,
    pub is_initialized: bool,
}

/// Marker component to trigger IK setup for quadrupeds
#[derive(Component)]
pub struct SetupQuadrupedIk;

/// System to set up IK chains for quadrupeds using k crate
pub fn setup_quadruped_ik_system(
    mut commands: Commands,
    query: Query<
        (Entity, &DeerQuadrupedBaseArmature),
        (With<SetupQuadrupedIk>, Without<QuadrupedIkController>),
    >,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    transform_query: Query<&Transform>,
) {
    for (entity, armature) in query.iter() {
        // Get foot entities from armature
        let foot_entities = [
            armature.get_front_toe_l(),
            armature.get_front_toe_r(),
            armature.get_toe_l(),
            armature.get_toe_r(),
        ];

        let mut leg_entities = [
            armature.get_front_shoulder_l(),
            armature.get_front_shoulder_r(),
            armature.get_shoulder_l(),
            armature.get_shoulder_r(),
        ];

        for (i, &foot_entity) in foot_entities.iter().enumerate() {
            let leg_type = match i {
                0 => LegType::FrontLeft,
                1 => LegType::FrontRight,
                2 => LegType::BackLeft,
                3 => LegType::BackRight,
                _ => unreachable!(),
            };

            // Create IK target sphere
            let target_entity = commands
                .spawn((
                    Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(8, 8))),
                    MeshMaterial3d(materials.add(StandardMaterial {
                        base_color: Color::srgb(1.0, 0.2, 0.2),
                        ..default()
                    })),
                    Transform::default(),
                    Name::new(format!("{:?} IK Target", leg_type)),
                ))
                .id();

            // Get rest position for this foot
            let rest_position =
                if let Ok(foot_transform) = transform_query.get(foot_entity) {
                    foot_transform.translation
                } else {
                    Vec3::ZERO
                };

            // Create foot IK chain component
            let foot_ik_chain = FootIkChain {
                leg_type,
                foot_entity,
                target_entity,
                current_target: rest_position,
                rest_position,
                step_height: 0.3,
                max_reach: 1.5,
                foot_state: FootState::default(),
                ..default()
            };

            // Create leg entity to hold the IK chain
            let leg_entity = commands
                .spawn((
                    foot_ik_chain,
                    Name::new(format!("{:?} Leg Controller", leg_type)),
                ))
                .id();

            // Make target a child of the main entity
            commands.entity(target_entity).insert(ChildOf(entity));
            commands.entity(leg_entity).insert(ChildOf(entity));

            leg_entities[i] = leg_entity;
        }

        // Add the main quadruped controller
        commands.entity(entity).insert((
            QuadrupedIkController {
                legs: leg_entities,
                body_entity: entity,
                is_initialized: true,
            },
            GaitController::default(),
        ));

        commands.entity(entity).remove::<SetupQuadrupedIk>();
        info!("Successfully set up quadruped IK for entity {:?}", entity);
    }
}

/// System to update gait patterns and foot states
pub fn update_gait_system(
    mut gait_query: Query<(&mut GaitController, &LinearVelocity)>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (mut gait, velocity) in gait_query.iter_mut() {
        let speed = velocity.0.length();
        gait.is_moving = speed > 0.1;

        if gait.is_moving {
            gait.current_phase += delta_time.0 * gait.gait_speed;
            if gait.current_phase >= 1.0 {
                gait.current_phase -= 1.0;
            }
        }
    }
}

/// Main system to update quadruped IK using k crate
pub fn update_quadruped_ik_system(
    mut ik_query: Query<(
        &QuadrupedIkController,
        &GaitController,
        &GlobalTransform,
        &LinearVelocity,
    )>,
    mut foot_query: Query<&mut FootIkChain>,
    mut set: ParamSet<(
        Query<(&Name, &mut Transform)>,
        Query<&mut Transform, Without<FootIkChain>>,
    )>,
    terrain_system: TerrainSystemParams,
    spatial_query: SpatialQuery,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (controller, gait, body_transform, velocity) in ik_query.iter_mut() {
        if !controller.is_initialized {
            continue;
        }

        // Update each leg
        for &leg_entity in &controller.legs {
            if let Ok(mut foot_chain) = foot_query.get_mut(leg_entity) {
                update_foot_ik_chain_k(
                    &mut foot_chain,
                    gait,
                    body_transform,
                    velocity,
                    &mut set.p1(),
                    &terrain_system,
                    &spatial_query,
                    delta_time.0,
                );

                if let Some(serial) = foot_chain.k_serial_chain.as_ref() {
                    // Foot bone
                    // if let Ok((_n, mut tf)) = set.p0().get_mut(foot_chain.foot_entity) {
                    //     if let Some(node) = serial.find(&foot_chain.k_chain_tail_node_name) {
                    //         let m = node.world_transform().unwrap();
                    //         tf.translation = Vec3::new(m.translation.x, m.translation.y, m.translation.z);
                    //         let r = m.rotation; tf.rotation = Quat::from_xyzw(r.i, r.j, r.k, r.w);
                    //     }
                    // }
                    // Intermediate bones
                    for (name, bone) in &foot_chain.tracked_bones {
                        let node_name = format!("{}_yaw", name.as_str());
                        if let Some(node) = serial.find(&node_name) {
                            let m = node.world_transform().unwrap();

                            if let Ok((_, mut tf)) = set.p0().get_mut(*bone) {
                                tf.translation = Vec3::new(
                                    m.translation.x,
                                    m.translation.y,
                                    m.translation.z,
                                );
                                let r = m.rotation;
                                tf.rotation = Quat::from_xyzw(r.i, r.j, r.k, r.w);
                            };
                        }
                    }
                }

                // if let Some(chain) = foot_chain.k_serial_chain.as_ref() {
                //     if let Ok((_name, ref mut foot_tf)) =
                //         set.p0().get_mut(foot_chain.foot_entity)
                //     {
                //         if let Some(end_node) =
                //             chain.find(&foot_chain.k_chain_tail_node_name)
                //         {
                //             let tf = end_node.world_transform().unwrap();
                //             foot_tf.translation = Vec3::new(
                //                 tf.translation.x,
                //                 tf.translation.y,
                //                 tf.translation.z,
                //             );
                //             let r = tf.rotation;
                //             foot_tf.rotation = Quat::from_xyzw(r.i, r.j, r.k, r.w);
                //         }
                //     }
                //
                //     // Update each intermediate bone using its `_yaw` node
                //     let mut current = foot_chain.foot_entity;
                //     for _ in 0..foot_chain.joint_count {
                //         // get bone name
                //         if let Ok(name) =
                //             set.p0().get(current).map(|(n, _)| n.as_str().to_string())
                //         {
                //             let node_name = format!("{}_yaw", name);
                //             if let Some(node) = chain.find(&node_name) {
                //                 let tf = node.world_transform().unwrap();
                //                 if let Ok(mut bone_tf) = set.p1().get_mut(current) {
                //                     bone_tf.translation = Vec3::new(
                //                         tf.translation.x,
                //                         tf.translation.y,
                //                         tf.translation.z,
                //                     );
                //                     let r = tf.rotation;
                //                     bone_tf.rotation =
                //                         Quat::from_xyzw(r.i, r.j, r.k, r.w);
                //                 }
                //             }
                //         }
                //
                //         // climb up to parent bone
                //         if let Ok(child_of) = set.p0().get(current).map(|(_, t)| t) {}
                //         // Note: you may need to track parent entity via your own Component or resource
                //         // to climb the hierarchy, e.g. storing bone_entities in FootIkChain.
                //     }
                // }

                // if let Some(chain) = foot_chain.k_serial_chain.as_ref() {
                //     if let Ok((_name, ref mut transform)) =
                //         &mut set.p0().get_mut(foot_chain.foot_entity)
                //     {
                //         if let Some(end_node) =
                //             chain.find(&foot_chain.k_chain_tail_node_name)
                //         {
                //             let tf = end_node.world_transform().unwrap();
                //             transform.translation = Vec3::new(
                //                 tf.translation.x,
                //                 tf.translation.y,
                //                 tf.translation.z,
                //             );
                //
                //             let r = tf.rotation;
                //             transform.rotation = Quat::from_xyzw(r.i, r.j, r.k, r.w);
                //         }
                //     }
                // }
            }
        }
    }
}

fn update_foot_ik_chain_k(
    foot_chain: &mut FootIkChain,
    gait: &GaitController,
    body_transform: &GlobalTransform,
    velocity: &LinearVelocity,
    target_transforms: &mut Query<&mut Transform, Without<FootIkChain>>,
    terrain_system: &TerrainSystemParams,
    spatial_query: &SpatialQuery,
    delta_time: f32,
) {
    // Calculate target position based on gait pattern
    let target_pos = calculate_foot_target_position_k(
        foot_chain,
        gait,
        body_transform,
        velocity,
        terrain_system,
        spatial_query,
    );

    // Update foot state and target
    update_foot_state_k(foot_chain, target_pos, gait, delta_time);

    // Apply target to visual target
    if let Ok(mut target_transform) = target_transforms.get_mut(foot_chain.target_entity)
    {
        target_transform.translation = foot_chain.current_target;
    }

    // Solve IK using k crate
    if let (Some(chain), Some(serial)) =
        (&mut foot_chain.k_chain, &mut foot_chain.k_serial_chain)
    {
        // Only solve if target moved significantly (optionally add threshold check)
        serial.update_transforms();
        let end = serial.find(&foot_chain.k_chain_tail_node_name).unwrap();
        let mut goal = end.world_transform().unwrap();
        goal.translation.x = foot_chain.current_target.x;
        goal.translation.y = foot_chain.current_target.y;
        goal.translation.z = foot_chain.current_target.z;

        // if let Some(root) = chain.find("fixed") {
        //     let rt = root.world_transform().unwrap();
        //     let root_pos =
        //         Vec3::new(rt.translation.x, rt.translation.y, rt.translation.z);
        //     let dir = foot_chain.current_target - root_pos;
        //     let dist = dir.length();
        //     if dist > foot_chain.max_reach {
        //         let clamped = root_pos + dir.normalize() * foot_chain.max_reach;
        //         goal.translation.x = clamped.x;
        //         goal.translation.y = clamped.y;
        //         goal.translation.z = clamped.z;
        //     }
        // }

        // Define constraints to ignore end-effector rotation for simpler solves
        let constraints = k::Constraints {
            rotation_x: false,
            // rotation_y: false,
            rotation_z: false,
            ..default()
        };

        // foot_chain.k_solver.allowable_target_distance = 0.01;
        // foot_chain.k_solver.num_max_try = 100;

        // Solve full 6-DOF IK with constraints
        if let Err(e) =
            foot_chain
                .k_solver
                .solve(&serial, &goal)
        {
            warn!("IK solve failed: {:?}", e);
        }
        // Apply solved transforms
        chain.update_transforms();
    }

    // if let (Some(_chain), Some(serial_chain)) =
    //     (&foot_chain.k_chain, &foot_chain.k_serial_chain)
    // {
    //     let end = serial_chain
    //         .find(&foot_chain.k_chain_last_node_name)
    //         .unwrap();
    //     serial_chain.update_transforms();
    //
    //     let target = end.world_transform().unwrap();
    //
    //     let constraints = k::Constraints {
    //         // rotation_x: false,
    //         // rotation_z: false,
    //         ..Default::default()
    //     };
    //
    //     // Solve IK
    //     if let Err(e) = foot_chain.k_solver.solve_with_constraints(
    //         serial_chain,
    //         &target,
    //         &constraints,
    //     ) {
    //         warn!("IK solve failed for {:?}: {:?}", foot_chain.leg_type, e);
    //     }
    //
    //     serial_chain.update_transforms();
    // }
}

fn calculate_foot_target_position_k(
    foot_chain: &FootIkChain,
    gait: &GaitController,
    body_transform: &GlobalTransform,
    velocity: &LinearVelocity,
    terrain_system: &TerrainSystemParams,
    spatial_query: &SpatialQuery,
) -> Vec3 {
    use crate::systems::ik_chain::gait_patterns::*;

    if !gait.is_moving {
        return foot_chain.rest_position;
    }

    // Calculate base foot position relative to body
    let body_pos = body_transform.translation();
    let movement_dir = velocity.0.normalize_or_zero();

    // Get gait pattern timing for this leg
    let leg_phase = get_leg_phase_offset(foot_chain.leg_type, gait.current_phase);

    // Calculate stride position
    let stride_offset = calculate_stride_offset(
        foot_chain.leg_type,
        leg_phase,
        movement_dir,
        gait.step_length,
    );

    // Calculate base target position
    let base_target = body_pos + foot_chain.rest_position + stride_offset;

    // Sample ground height at target position
    let ground_height =
        sample_ground_height_k(base_target, spatial_query, terrain_system);

    Vec3::new(base_target.x, ground_height, base_target.z)
}

fn update_foot_state_k(
    foot_chain: &mut FootIkChain,
    target_pos: Vec3,
    gait: &GaitController,
    delta_time: f32,
) {
    use crate::systems::ik_chain::gait_patterns::*;

    let should_step = should_foot_step(foot_chain, target_pos, gait);
    let mut new_foot_state = foot_chain.foot_state;

    match &mut foot_chain.foot_state {
        FootState::Planted { time_planted } => {
            *time_planted += delta_time;

            if should_step {
                new_foot_state = FootState::Lifting { progress: 0.0 };
            } else {
                foot_chain.current_target = target_pos;
            }
        }

        FootState::Lifting { progress } => {
            *progress += delta_time / (gait.step_duration * 0.2);

            if *progress >= 1.0 {
                foot_chain.foot_state = FootState::Swinging {
                    progress: 0.0,
                    target_pos,
                };
            } else {
                let lift_height = (*progress * gait.step_height).min(gait.step_height);
                foot_chain.current_target.y = target_pos.y + lift_height;
            }
        }

        FootState::Swinging {
            progress,
            target_pos: swing_target,
        } => {
            *progress += delta_time / (gait.step_duration * 0.6);

            if *progress >= 1.0 {
                foot_chain.foot_state = FootState::Placing {
                    progress: 0.0,
                    target_pos: *swing_target,
                };
            } else {
                let start_pos = foot_chain.current_target;
                let arc_height =
                    gait.step_height * (1.0 - (2.0 * *progress - 1.0).powi(2));

                foot_chain.current_target = start_pos.lerp(*swing_target, *progress);
                foot_chain.current_target.y = swing_target.y + arc_height;
            }
        }

        FootState::Placing {
            progress,
            target_pos: place_target,
        } => {
            *progress += delta_time / (gait.step_duration * 0.2);

            if *progress >= 1.0 {
                new_foot_state = FootState::Planted { time_planted: 0.0 };
                foot_chain.current_target = *place_target;
            } else {
                let current_height = foot_chain.current_target.y;
                let target_height = place_target.y;
                foot_chain.current_target.y =
                    current_height.lerp(target_height, *progress);
            }
        }
    }

    foot_chain.foot_state = new_foot_state;
}

fn sample_ground_height_k(
    position: Vec3,
    spatial_query: &SpatialQuery,
    _terrain_system: &TerrainSystemParams,
) -> f32 {
    // Raycast downward to find ground
    let ray_start = Vec3::new(position.x, position.y + 2.0, position.z);
    let ray_direction = Dir3::NEG_Y;

    if let Some(hit) = spatial_query.cast_ray(
        ray_start,
        ray_direction,
        5.0,
        true,
        &SpatialQueryFilter::default(),
    ) {
        return ray_start.y - hit.distance; // Return actual hit position Y
    }

    // Fallback to terrain height or current position
    position.y
}
