use bevy::prelude::*;
use avian3d::prelude::*;
use crate::systems::environment::system_params::TerrainSystemParams;

/// Configuration for ground detection
#[derive(Debug, <PERSON>lone)]
pub struct GroundDetectionConfig {
    pub max_ray_distance: f32,
    pub ray_start_height: f32,
    pub terrain_sample_radius: f32,
    pub slope_threshold: f32,
}

impl Default for GroundDetectionConfig {
    fn default() -> Self {
        Self {
            max_ray_distance: 10.0,
            ray_start_height: 3.0,
            terrain_sample_radius: 0.5,
            slope_threshold: 0.7, // ~35 degrees
        }
    }
}

/// Result of ground detection
#[derive(Debug, <PERSON>lone)]
pub struct GroundInfo {
    pub height: f32,
    pub normal: Vec3,
    pub slope: f32,
    pub is_stable: bool,
    pub surface_type: SurfaceType,
}

#[derive(Debug, <PERSON>lone, Copy, PartialEq)]
pub enum SurfaceType {
    Solid,
    Water,
    Mud,
    Sand,
    Rock,
    Grass,
}

impl Default for GroundInfo {
    fn default() -> Self {
        Self {
            height: 0.0,
            normal: Vec3::Y,
            slope: 0.0,
            is_stable: true,
            surface_type: SurfaceType::Solid,
        }
    }
}

/// Advanced ground detection with multiple sampling points
pub fn detect_ground_advanced(
    position: Vec3,
    spatial_query: &SpatialQuery,
    terrain_system: &TerrainSystemParams,
    config: &GroundDetectionConfig,
) -> GroundInfo {
    // Primary raycast straight down
    let primary_hit = raycast_ground(position, spatial_query, config);
    
    if let Some(hit_info) = primary_hit {
        // Sample surrounding points to calculate slope and stability
        let surrounding_heights = sample_surrounding_heights(
            position,
            spatial_query,
            terrain_system,
            config,
        );
        
        let (normal, slope) = calculate_surface_normal_and_slope(&surrounding_heights, position);
        let is_stable = slope < config.slope_threshold;
        
        GroundInfo {
            height: position.y + config.ray_start_height - hit_info.distance,
            normal,
            slope,
            is_stable,
            surface_type: determine_surface_type(&hit_info),
        }
    } else {
        // Fallback to terrain system
        fallback_terrain_detection(position, terrain_system)
    }
}

/// Simple ground height detection
pub fn detect_ground_height(
    position: Vec3,
    spatial_query: &SpatialQuery,
    terrain_system: &TerrainSystemParams,
) -> f32 {
    let config = GroundDetectionConfig::default();
    detect_ground_advanced(position, spatial_query, terrain_system, &config).height
}

/// Raycast to find ground collision
fn raycast_ground(
    position: Vec3,
    spatial_query: &SpatialQuery,
    config: &GroundDetectionConfig,
) -> Option<RayHitData> {
    let ray_start = Vec3::new(
        position.x,
        position.y + config.ray_start_height,
        position.z,
    );
    
    spatial_query.cast_ray(
        ray_start,
        Dir3::NEG_Y,
        config.max_ray_distance,
        true,
        &SpatialQueryFilter::default(),
    )
}

/// Sample heights at multiple points around the position
fn sample_surrounding_heights(
    center: Vec3,
    spatial_query: &SpatialQuery,
    terrain_system: &TerrainSystemParams,
    config: &GroundDetectionConfig,
) -> Vec<(Vec3, f32)> {
    let mut heights = Vec::new();
    let radius = config.terrain_sample_radius;
    
    // Sample in a cross pattern
    let sample_points = [
        Vec3::new(center.x + radius, center.y, center.z),     // East
        Vec3::new(center.x - radius, center.y, center.z),     // West
        Vec3::new(center.x, center.y, center.z + radius),     // North
        Vec3::new(center.x, center.y, center.z - radius),     // South
        Vec3::new(center.x + radius * 0.7, center.y, center.z + radius * 0.7), // NE
        Vec3::new(center.x - radius * 0.7, center.y, center.z + radius * 0.7), // NW
        Vec3::new(center.x + radius * 0.7, center.y, center.z - radius * 0.7), // SE
        Vec3::new(center.x - radius * 0.7, center.y, center.z - radius * 0.7), // SW
    ];
    
    for point in sample_points {
        if let Some(hit) = raycast_ground(point, spatial_query, config) {
            let hit_height = point.y + config.ray_start_height - hit.distance;
            heights.push((point, hit_height));
        } else {
            // Fallback to terrain system or use center height
            heights.push((point, center.y));
        }
    }
    
    heights
}

/// Calculate surface normal and slope from surrounding height samples
fn calculate_surface_normal_and_slope(
    height_samples: &[(Vec3, f32)],
    center: Vec3,
) -> (Vec3, f32) {
    if height_samples.len() < 3 {
        return (Vec3::Y, 0.0);
    }
    
    // Use cross product of two vectors to find normal
    let p1 = height_samples[0];
    let p2 = height_samples[1];
    let p3 = height_samples[2];
    
    let v1 = Vec3::new(p2.0.x - p1.0.x, p2.1 - p1.1, p2.0.z - p1.0.z);
    let v2 = Vec3::new(p3.0.x - p1.0.x, p3.1 - p1.1, p3.0.z - p1.0.z);
    
    let normal = v1.cross(v2).normalize_or(Vec3::Y);
    let slope = normal.angle_between(Vec3::Y);
    
    (normal, slope)
}

/// Determine surface type based on hit information
fn determine_surface_type(hit_info: &RayHitData) -> SurfaceType {
    // This would be expanded based on material properties, entity components, etc.
    // For now, return a default
    SurfaceType::Solid
}

/// Fallback terrain detection when raycasting fails
fn fallback_terrain_detection(
    position: Vec3,
    terrain_system: &TerrainSystemParams,
) -> GroundInfo {
    // This would sample from the terrain height map
    // For now, return a basic ground info
    GroundInfo {
        height: 0.0, // Would be sampled from terrain
        normal: Vec3::Y,
        slope: 0.0,
        is_stable: true,
        surface_type: SurfaceType::Grass,
    }
}

/// Predict ground height at a future position
pub fn predict_ground_height(
    current_pos: Vec3,
    velocity: Vec3,
    prediction_time: f32,
    spatial_query: &SpatialQuery,
    terrain_system: &TerrainSystemParams,
) -> f32 {
    let future_pos = current_pos + velocity * prediction_time;
    detect_ground_height(future_pos, spatial_query, terrain_system)
}

/// Check if a position is suitable for foot placement
pub fn is_suitable_foot_placement(
    position: Vec3,
    spatial_query: &SpatialQuery,
    terrain_system: &TerrainSystemParams,
    config: &GroundDetectionConfig,
) -> bool {
    let ground_info = detect_ground_advanced(position, spatial_query, terrain_system, config);
    
    ground_info.is_stable && 
    ground_info.slope < config.slope_threshold &&
    matches!(ground_info.surface_type, SurfaceType::Solid | SurfaceType::Grass | SurfaceType::Rock)
}

/// Find the best foot placement position within a search radius
pub fn find_best_foot_placement(
    ideal_position: Vec3,
    search_radius: f32,
    spatial_query: &SpatialQuery,
    terrain_system: &TerrainSystemParams,
) -> Vec3 {
    let config = GroundDetectionConfig::default();
    
    // Start with the ideal position
    if is_suitable_foot_placement(ideal_position, spatial_query, terrain_system, &config) {
        return ideal_position;
    }
    
    // Search in a spiral pattern
    let search_points = 8;
    for i in 1..=search_points {
        let radius = search_radius * (i as f32 / search_points as f32);
        
        for j in 0..8 {
            let angle = j as f32 * std::f32::consts::TAU / 8.0;
            let offset = Vec3::new(
                angle.cos() * radius,
                0.0,
                angle.sin() * radius,
            );
            
            let test_pos = ideal_position + offset;
            if is_suitable_foot_placement(test_pos, spatial_query, terrain_system, &config) {
                return test_pos;
            }
        }
    }
    
    // If no suitable position found, return the ideal position
    ideal_position
}

/// Calculate terrain roughness in an area
pub fn calculate_terrain_roughness(
    center: Vec3,
    radius: f32,
    spatial_query: &SpatialQuery,
    terrain_system: &TerrainSystemParams,
) -> f32 {
    let config = GroundDetectionConfig {
        terrain_sample_radius: radius,
        ..Default::default()
    };
    
    let height_samples = sample_surrounding_heights(center, spatial_query, terrain_system, &config);
    
    if height_samples.len() < 2 {
        return 0.0;
    }
    
    // Calculate variance in heights
    let heights: Vec<f32> = height_samples.iter().map(|(_, h)| *h).collect();
    let mean_height = heights.iter().sum::<f32>() / heights.len() as f32;
    
    let variance = heights.iter()
        .map(|h| (h - mean_height).powi(2))
        .sum::<f32>() / heights.len() as f32;
    
    variance.sqrt()
}
