use crate::components::animals::{
    AnimalBaseArmature, AnimalSpeciesModelKey, AnimalSpeciesModelSet, AutoRigColliders, AutoSetupIK, MovementDampingFactor
};
use crate::libraries::blender_ik::{
    Blender<PERSON>k<PERSON>hain, BlenderIkConstraint, BlenderIkJoint, BlenderIkSolution, BlenderIkTarget
};
use crate::libraries::inverse_kinematics::constraint::{IkSwingConstraint, IkTwistConstraint};
use crate::libraries::ik_fabrik::chain::IkChain;
use crate::libraries::ik_fabrik::constraint::{SwingConstraint, TwistConstraint};
use crate::libraries::inverse_kinematics;
use crate::libraries::inverse_kinematics::IkConstraint;
use crate::resources::WorldSimulationDeltaTime;
use crate::systems::animals::system_params::AnimalsAssetSystemParams;
use crate::systems::environment::system_params::TerrainSystemParams;
use avian3d::prelude::*;
use bevy::prelude::*;
use bevy::render::primitives::Aabb;
use bevy_descendant_collector::{DescendantCollectorTarget, DescendantLoader};
use bevy_gltf_animation::prelude::{GltfAnimations, GltfSceneRoot};

pub mod animation_blending;
pub mod animation_controller;
pub mod bees;
pub mod system_params;

/// Component to mark an IK target for foot placement
#[derive(Component, Reflect, Debug)]
#[reflect(Component)]
pub struct FootIkTarget {
    /// Which foot this target belongs to
    pub foot_type: FootType,
    /// The entity this target is following (the foot/toe entity)
    pub foot_entity: Entity,
    /// Offset from the foot position when placing on ground
    pub ground_offset: Vec3,
    /// Maximum distance the foot can reach
    pub max_reach: f32,
    /// How smoothly the target moves (0.0 = instant, 1.0 = very smooth)
    pub smoothing: f32,
}

/// Identifies which foot this is for
#[derive(Debug, Clone, Copy, PartialEq, Eq, Reflect)]
pub enum FootType {
    LeftFront,
    RightFront,
    LeftBack,
    RightBack,
}

/// Component for animals that have foot IK enabled
#[derive(Component, Reflect, Debug)]
#[reflect(Component)]
pub struct FootIkController {
    /// IK targets for each foot
    pub foot_targets: Vec<Entity>,
    /// How far ahead to place feet when walking
    pub stride_length: f32,
    /// How high to lift feet when stepping
    pub step_height: f32,
    /// Speed of foot placement
    pub placement_speed: f32,
}

pub fn set_model_for_species(
    mut commands: Commands,
    animals_assets: AnimalsAssetSystemParams,
    query: Query<(Entity, &AnimalSpeciesModelKey), Without<AnimalSpeciesModelSet>>,
) {
    for (entity, species) in query.iter() {
        let Some(model) = animals_assets.get_model(&species.0, None) else {
            log::warn!("Failed to load model for species: {:?}", species.0);
            continue;
        };

        commands
            .entity(entity)
            .insert((AnimalSpeciesModelSet, model));
    }
}

pub fn apply_movement_damping(
    mut query: Query<(&MovementDampingFactor, &mut LinearVelocity)>,
) {
    for (damping_factor, mut linear_velocity) in &mut query {
        linear_velocity.x *= damping_factor.0;
        linear_velocity.z *= damping_factor.0;
    }
}

fn local_transform(child: &GlobalTransform, parent: &GlobalTransform) -> Transform {
    let mats = parent.compute_matrix().inverse() * child.compute_matrix();
    let (scale, rotation, translation) = mats.to_scale_rotation_translation();
    Transform {
        translation,
        rotation,
        scale,
    }
}

/// Convert a quaternion to Euler angles (XYZ order)
/// Returns (x_rotation, y_rotation, z_rotation) in radians
fn quat_to_euler_xyz(quat: Quat) -> (f32, f32, f32) {
    // Convert Bevy Quat to Euler angles using XYZ rotation order
    // This follows the standard aerospace sequence: roll (X), pitch (Y), yaw (Z)

    let w = quat.w;
    let x = quat.x;
    let y = quat.y;
    let z = quat.z;

    // Roll (X-axis rotation)
    let sin_r_cp = 2.0 * (w * x + y * z);
    let cos_r_cp = 1.0 - 2.0 * (x * x + y * y);
    let roll = sin_r_cp.atan2(cos_r_cp);

    // Pitch (Y-axis rotation)
    let sin_p = 2.0 * (w * y - z * x);
    let pitch = if sin_p.abs() >= 1.0 {
        std::f32::consts::FRAC_PI_2.copysign(sin_p) // Use 90 degrees if out of range
    } else {
        sin_p.asin()
    };

    // Yaw (Z-axis rotation)
    let sin_y_cp = 2.0 * (w * z + x * y);
    let cos_y_cp = 1.0 - 2.0 * (y * y + z * z);
    let yaw = sin_y_cp.atan2(cos_y_cp);

    (roll, pitch, yaw)
}

pub fn on_auto_setup_ik2<Armature>(
    mut commands: Commands,
    query: Query<
        (Entity, &GlobalTransform, &Armature),
        (With<AutoSetupIK>, With<Armature>),
    >,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    transforms: Query<&GlobalTransform>,
) where
    Armature: Component + AnimalBaseArmature,
{
    for (entity, global_transform, armature) in query.iter() {
        let mut foot_targets = Vec::new();

        let legs = [(armature.get_foot_l(), FootType::LeftBack)];

        for (foot_entity, foot_type) in legs {
            let toe = match foot_type {
                FootType::LeftFront => armature.get_front_toe_l(),
                FootType::RightFront => armature.get_front_toe_r(),
                FootType::LeftBack => armature.get_toe_l(),
                FootType::RightBack => armature.get_toe_r(),
            };

            let foot_gt = transforms.get(foot_entity).unwrap();
            // let foot_aabb = aabbs.get(foot_entity).unwrap();
            let toe_gt = transforms.get(toe).unwrap();

            let foot_target = commands
                .spawn((
                    Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(7, 7))),
                    MeshMaterial3d(materials.add(StandardMaterial {
                        base_color: Color::srgb(1.0, 0.0, 0.0),
                        ..default()
                    })),
                    Name::new("Left Foot IK Target"),
                    Transform::from_translation(toe_gt.translation()),
                ))
                .id();

            foot_targets.push(foot_target);

            // First, get all the joints in the chain
            let shin_entity = armature.get_shin_l();
            let thigh_entity = armature.get_thigh_l();
            let hip_entity = armature.get_shoulder_l(); // Or equivalent parent joint
            let root_entity = armature.get_root();

            let pole_target = commands
                .spawn((
                    Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(7, 7))),
                    MeshMaterial3d(materials.add(StandardMaterial {
                        base_color: Color::srgb(0.0, 1.0, 0.0),
                        ..default()
                    })),
                    Transform::from_xyz(-1.0, 0.4, -0.2),
                ))
                .id();

            // Use simple FABRIK IK with conservative parameters
            commands.entity(foot_entity).insert(IkConstraint {
                chain_length: 3,
                iterations: 10,  // Low iterations for stability
                target: foot_target,
                enabled: true,
                ..default()
            });

            commands.entity(entity).remove::<AutoSetupIK>();
        }
    }
}

pub fn on_auto_setup_ik<Armature>(
    mut commands: Commands,
    query: Query<
        (Entity, &GlobalTransform, &Armature),
        (With<AutoSetupIK>, With<Armature>),
    >,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    transforms: Query<&GlobalTransform>,
) where
    Armature: Component + AnimalBaseArmature,
{
    for (entity, global_transform, armature) in query.iter() {
        let mut foot_targets = Vec::new();

        let legs = [
            (armature.get_front_foot_l(), FootType::LeftFront),
            (armature.get_front_foot_r(), FootType::RightFront),
            (armature.get_foot_l(), FootType::LeftBack),
            (armature.get_foot_r(), FootType::RightBack),
        ];

        for (foot_entity, foot_type) in legs {
            let (toe_entity, _thigh_entity, _shin_entity) = match foot_type {
                FootType::LeftFront => (
                    armature.get_front_toe_l(),
                    armature.get_front_thigh_l(),
                    armature.get_front_shin_l(),
                ),
                FootType::RightFront => (
                    armature.get_front_toe_r(),
                    armature.get_front_thigh_r(),
                    armature.get_front_shin_r(),
                ),
                FootType::LeftBack => (
                    armature.get_toe_l(),
                    armature.get_thigh_l(),
                    armature.get_shin_l(),
                ),
                FootType::RightBack => (
                    armature.get_toe_r(),
                    armature.get_thigh_r(),
                    armature.get_shin_r(),
                ),
            };

            let foot_gt = transforms.get(foot_entity).unwrap();
            let toe_gt = transforms.get(toe_entity).unwrap();

            let foot_target = commands
                .spawn((
                    Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(7, 7))),
                    MeshMaterial3d(materials.add(StandardMaterial {
                        base_color: match foot_type {
                            FootType::LeftFront => Color::srgb(1.0, 0.0, 0.0),   // Red
                            FootType::RightFront => Color::srgb(0.0, 1.0, 0.0),  // Green
                            FootType::LeftBack => Color::srgb(0.0, 0.0, 1.0),    // Blue
                            FootType::RightBack => Color::srgb(1.0, 1.0, 0.0),   // Yellow
                        },
                        ..default()
                    })),
                    Name::new(format!("{:?} Foot IK Target", foot_type)),
                    Transform::from_translation(toe_gt.translation()),
                    FootIkTarget {
                        foot_type,
                        foot_entity,
                        ground_offset: Vec3::new(0.0, 0.05, 0.0), // Slightly above ground
                        max_reach: 2.0,
                        smoothing: 0.9, // High smoothing for stability
                    },
                ))
                .id();

            foot_targets.push(foot_target);

            // Calculate proper pole target position to control knee bending direction
            let pole_offset = match foot_type {
                FootType::LeftFront | FootType::RightFront => {
                    // Front legs: pole target should be forward and slightly up to make knees bend forward
                    Vec3::new(0.0, 0.3, 0.8)
                },
                FootType::LeftBack | FootType::RightBack => {
                    // Back legs: pole target should be backward and slightly up to make knees bend backward
                    Vec3::new(0.0, 0.3, -0.8)
                }
            };

            let pole_target_position = foot_gt.translation() + pole_offset;

            let pole_target = commands
                .spawn((
                    Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(7, 7))),
                    MeshMaterial3d(materials.add(StandardMaterial {
                        base_color: Color::srgb(0.0, 1.0, 0.0),
                        ..default()
                    })),
                    Name::new(format!("{:?} Pole Target", foot_type)),
                    Transform::from_translation(pole_target_position),
                ))
                .id();

            // Add joint constraints to knee joints to limit their rotation
            let shin_entity = match foot_type {
                FootType::LeftFront => armature.get_front_shin_l(),
                FootType::RightFront => armature.get_front_shin_r(),
                FootType::LeftBack => armature.get_shin_l(),
                FootType::RightBack => armature.get_shin_r(),
            };

            // Add swing constraint to knee joint to limit side-to-side movement
            commands.entity(shin_entity).insert(IkSwingConstraint {
                angle: std::f32::consts::FRAC_PI_6, // 30 degrees max swing
                axis: Vec3::Y, // Constrain around Y axis
            });

            // Add twist constraint to prevent knee from twisting too much
            commands.entity(shin_entity).insert(IkTwistConstraint {
                max_angle: std::f32::consts::FRAC_PI_8, // 22.5 degrees max twist
                min_angle: -std::f32::consts::FRAC_PI_8, // -22.5 degrees min twist
                axis: Vec3::Y, // Twist around Y axis
            });

            // Enable pole target and configure IK constraint properly
            commands.entity(toe_entity).insert(IkConstraint {
                chain_length: 3,
                iterations: 50,  // Reduced iterations for better stability
                target: foot_target,
                // pole_target: Some(pole_target), // Enable pole target (Entity, not Vec3)
                // pole_angle: -std::f32::consts::FRAC_PI_2,
                enabled: true,
                use_tail: true,
                ..default()
            });
        }

        // Add the foot IK controller to the animal with very conservative settings
        // commands.entity(entity).insert(FootIkController {
        //     foot_targets,
        //     stride_length: 0.1,  // Very small stride for stability
        //     step_height: 0.05,   // Very small step height
        //     placement_speed: 0.5, // Very slow placement for maximum stability
        // });

        commands.entity(entity).remove::<AutoSetupIK>();
    }
}

/// System to update foot IK targets based on ground detection and animal movement
pub fn update_foot_ik_targets(
    mut ik_target_query: Query<(&mut Transform, &FootIkTarget)>,
    mut ik_chain_query: Query<&mut IkChain>,
    foot_controller_query: Query<(&FootIkController, &GlobalTransform, &LinearVelocity)>,
    foot_transforms: Query<&GlobalTransform>,
    terrain_system: TerrainSystemParams,
    spatial_query: SpatialQuery,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (controller, animal_transform, velocity) in foot_controller_query.iter() {
        let animal_pos = animal_transform.translation();
        let movement_direction = velocity.0.normalize_or_zero();

        for (i, &target_entity) in controller.foot_targets.iter().enumerate() {
            if let Ok((mut target_transform, foot_target)) =
                ik_target_query.get_mut(target_entity)
            {
                // Very simple approach: keep targets mostly stationary with minimal ground adjustment
                let ground_height = detect_ground_height(
                    target_transform.translation,
                    &spatial_query,
                    &terrain_system,
                );

                // Only adjust Y position for ground height, keep X and Z stable
                let target_y = ground_height + foot_target.ground_offset.y;
                let current_pos = target_transform.translation;

                // Very slow Y adjustment only
                let new_y = current_pos.y.lerp(target_y, 0.01);
                target_transform.translation.y = new_y;
            }
        }
    }
}

/// Get the rest position offset for each foot type relative to the animal's body center
fn get_foot_rest_offset(foot_type: FootType) -> Vec3 {
    match foot_type {
        FootType::LeftFront => Vec3::new(0.3, -0.5, 0.4),   // Front left
        FootType::RightFront => Vec3::new(-0.3, -0.5, 0.4), // Front right
        FootType::LeftBack => Vec3::new(0.3, -0.5, -0.4),   // Back left
        FootType::RightBack => Vec3::new(-0.3, -0.5, -0.4), // Back right
    }
}

/// System to update Blender IK targets from foot IK targets
pub fn update_blender_ik_targets(
    foot_targets: Query<(Entity, &Transform, &FootIkTarget), Changed<Transform>>,
    mut blender_chains: Query<&mut BlenderIkChain>,
) {
    // Update Blender IK chain targets to match foot IK targets
    for (target_entity, foot_transform, foot_target) in foot_targets.iter() {
        for mut blender_chain in blender_chains.iter_mut() {
            if blender_chain.target == target_entity {
                // Update the Blender IK chain's target position
                // The Blender IK solver will read from the target entity's transform
                continue;
            }
        }
    }
}

/// Detect ground height at a given position using raycasting and terrain sampling
fn detect_ground_height(
    position: Vec3,
    spatial_query: &SpatialQuery,
    terrain_system: &TerrainSystemParams,
) -> f32 {
    // Start raycast from above the position
    let ray_start = Vec3::new(position.x, position.y + 5.0, position.z);
    let ray_direction = -Dir3::Y;
    let max_distance = 10.0;

    // Perform raycast to find ground
    if let Some(hit) = spatial_query.cast_ray(
        ray_start,
        ray_direction,
        max_distance,
        true,
        &SpatialQueryFilter::default(),
    ) {
        return ray_start.y - hit.distance; // Return actual hit position Y
    }

    // Fallback: try to get terrain height from terrain system
    // This is a simplified approach - you might want to implement proper terrain height sampling
    if terrain_system.is_in_bounds(position) {
        // For now, return a default ground level
        // In a full implementation, you'd sample the terrain height map
        return 0.0;
    }

    // Final fallback
    position.y
}

pub fn on_added_gltf_scene<Animal, T>(
    mut commands: Commands,
    gltf_scenes: Query<
        (Entity, &Children),
        (With<Animal>, With<GltfSceneRoot>, Without<T>),
    >,
    children_query: Query<&Children>,
    name_query: Query<&Name>,
) where
    Animal: Component,
    T: Component + DescendantLoader,
{
    for (entity, children) in gltf_scenes.iter() {
        if children.is_empty() {
            continue;
        }

        for child in children_query.iter_descendants(entity) {
            if let Ok(name) = name_query.get(child) {
                if name.contains("Root") {
                    commands
                        .entity(entity)
                        .insert(DescendantCollectorTarget::<T>::default());

                    return;
                }
            }
        }
    }
}

// pub fn auto_rig_colliders<T>(
//     mut commands: Commands,
//     children: Query<&Children>,
//     mesh_query: Query<(&Aabb, &Mesh3d)>,
//     auto_rig_query: Query<&T, (With<AutoRigColliders>, Added<T>)>,
//     transforms: Query<&Transform>,
//     meshes: Res<Assets<Mesh>>,
// ) where
//     T: Component + DescendantLoader + AnimalBaseArmature,
// {
//     for armature in auto_rig_query.iter() {
//         let mut half_extents = Vec3A::ZERO;
//
//         if let Ok(children) = children.get(armature.get_mesh()) {
//             for child in children.iter() {
//                 if let Ok((aabb, mesh_3d)) = mesh_query.get(child) {
//                     // if let Some(mesh) = meshes.get(&mesh_3d.0) {
//                     //     if let Some(names) = mesh.morph_target_names() {
//                     //         log::info!("Mesh has morph targets: {:?}", names);
//                     //     }
//                     // }
//
//                     half_extents = aabb.half_extents;
//                     break;
//                 }
//             }
//         }
//
//         if half_extents == Vec3A::ZERO {
//             continue;
//         }
//
//         // Auto rotate by 180 degrees on the Y axis
//         if let Ok(base_transform) = transforms.get(armature.get_base()) {
//             commands.entity(armature.get_base()).insert(
//                 base_transform.with_rotation(Quat::from_rotation_y(std::f32::consts::PI)),
//             );
//         }
//
//         commands
//             .entity(armature.get_body())
//             .insert(Collider::cuboid(
//                 half_extents.x * 4.,
//                 half_extents.y * 4.,
//                 half_extents.z * 4.,
//             ));
//     }
// }
