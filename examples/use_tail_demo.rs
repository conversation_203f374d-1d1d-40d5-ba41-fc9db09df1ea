//! Demonstration of the "Use Tail" option in Blender IK
//! 
//! This example shows how to use the `use_tail` option to include
//! the bone's tail as the last element in the IK chain, matching
//! Blender's "Use Tail" feature.

use bevy::prelude::*;
use project_gaia_forge::libraries::blender_ik::*;

fn main() {
    App::new()
        .add_plugins((
            DefaultPlugins,
            BlenderIkPlugin,
        ))
        .add_systems(Startup, setup_use_tail_demo)
        .add_systems(Update, (
            move_target,
            display_info,
        ))
        .run();
}

fn setup_use_tail_demo(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    // Camera
    commands.spawn((
        Camera3d::default(),
        Transform::from_xyz(5.0, 5.0, 5.0).looking_at(Vec3::ZERO, Vec3::Y),
    ));

    // Light
    commands.spawn((
        DirectionalLight {
            shadows_enabled: true,
            ..default()
        },
        Transform::from_rotation(Quat::from_euler(EulerRot::XYZ, -0.5, -0.5, 0.0)),
    ));

    // Create materials
    let bone_material = materials.add(StandardMaterial {
        base_color: Color::srgb(0.8, 0.6, 0.4),
        ..default()
    });
    
    let target_material = materials.add(StandardMaterial {
        base_color: Color::srgb(1.0, 0.2, 0.2),
        ..default()
    });

    // Create bone mesh
    let bone_mesh = meshes.add(Cuboid::new(0.1, 0.1, 1.0));
    let target_mesh = meshes.add(Sphere::new(0.1));

    // Create a simple 3-joint arm
    let shoulder = commands.spawn((
        BlenderIkJoint::default(),
        Mesh3d(bone_mesh.clone()),
        MeshMaterial3d(bone_material.clone()),
        Transform::from_translation(Vec3::new(0.0, 0.0, 0.0)),
        GlobalTransform::default(),
        Name::new("Shoulder"),
    )).id();

    let elbow = commands.spawn((
        BlenderIkJoint::default(),
        Mesh3d(bone_mesh.clone()),
        MeshMaterial3d(bone_material.clone()),
        Transform::from_translation(Vec3::new(1.0, 0.0, 0.0)),
        GlobalTransform::default(),
        Name::new("Elbow"),
    )).id();

    let wrist = commands.spawn((
        BlenderIkJoint::default(),
        Mesh3d(bone_mesh.clone()),
        MeshMaterial3d(bone_material.clone()),
        Transform::from_translation(Vec3::new(2.0, 0.0, 0.0)),
        GlobalTransform::default(),
        Name::new("Wrist"),
    )).id();

    // Create target
    let target = commands.spawn((
        BlenderIkTarget {
            position: Vec3::new(2.5, 1.0, 0.0),
            rotation: None,
        },
        Mesh3d(target_mesh),
        MeshMaterial3d(target_material),
        Transform::from_translation(Vec3::new(2.5, 1.0, 0.0)),
        GlobalTransform::default(),
        Name::new("Target"),
        MovingTarget,
    )).id();

    // Create IK chain WITHOUT use_tail (traditional approach)
    commands.spawn((
        BlenderIkChain {
            joints: vec![shoulder, elbow, wrist],
            target,
            max_iterations: 20,
            tolerance: 0.001,
            use_sdls: true,
            joint_weights: vec![1.0, 0.8, 0.6],
            enabled: true,
            use_tail: false, // Traditional IK without tail
        },
        BlenderIkSolution::default(),
        Name::new("IK Chain (No Tail)"),
    ));

    // Create a second arm to demonstrate use_tail
    let shoulder2 = commands.spawn((
        BlenderIkJoint::default(),
        Mesh3d(bone_mesh.clone()),
        MeshMaterial3d(bone_material.clone()),
        Transform::from_translation(Vec3::new(0.0, 2.0, 0.0)),
        GlobalTransform::default(),
        Name::new("Shoulder2"),
    )).id();

    let elbow2 = commands.spawn((
        BlenderIkJoint::default(),
        Mesh3d(bone_mesh.clone()),
        MeshMaterial3d(bone_material.clone()),
        Transform::from_translation(Vec3::new(1.0, 2.0, 0.0)),
        GlobalTransform::default(),
        Name::new("Elbow2"),
    )).id();

    let wrist2 = commands.spawn((
        BlenderIkJoint::default(),
        Mesh3d(bone_mesh.clone()),
        MeshMaterial3d(bone_material.clone()),
        Transform::from_translation(Vec3::new(2.0, 2.0, 0.0)),
        GlobalTransform::default(),
        Name::new("Wrist2"),
    )).id();

    // Create IK chain WITH use_tail (Blender-style approach)
    commands.spawn((
        BlenderIkChain {
            joints: vec![shoulder2, elbow2, wrist2],
            target,
            max_iterations: 20,
            tolerance: 0.001,
            use_sdls: true,
            joint_weights: vec![1.0, 0.8, 0.6],
            enabled: true,
            use_tail: true, // Enable use_tail for more precise targeting
        }.with_use_tail(true), // Demonstrate builder method
        BlenderIkSolution::default(),
        Name::new("IK Chain (With Tail)"),
    ));

    // Add UI text
    commands.spawn((
        Text::new("Use Tail Demo\n\nBottom arm: use_tail = false\nTop arm: use_tail = true\n\nThe top arm should reach more precisely to the target."),
        Node {
            position_type: PositionType::Absolute,
            top: Val::Px(10.0),
            left: Val::Px(10.0),
            ..default()
        },
        TextColor(Color::WHITE),
    ));
}

#[derive(Component)]
struct MovingTarget;

fn move_target(
    time: Res<Time>,
    mut target_query: Query<(&mut Transform, &mut BlenderIkTarget), With<MovingTarget>>,
) {
    for (mut transform, mut target) in target_query.iter_mut() {
        let t = time.elapsed_secs();
        let new_pos = Vec3::new(
            2.5 + (t * 0.5).sin() * 0.5,
            1.0 + (t * 0.3).cos() * 0.5,
            (t * 0.7).sin() * 0.3,
        );
        
        transform.translation = new_pos;
        target.position = new_pos;
    }
}

fn display_info(
    chains: Query<(&BlenderIkChain, &Name)>,
) {
    for (chain, name) in chains.iter() {
        if chain.use_tail {
            info!("{}: Using tail for end effector calculation", name);
        } else {
            info!("{}: Using bone position for end effector calculation", name);
        }
    }
}
