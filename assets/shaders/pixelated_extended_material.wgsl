#import bevy_pbr::{
    pbr_fragment::pbr_input_from_standard_material,
    pbr_functions::alpha_discard,
    mesh_view_bindings::globals,
}

#ifdef PREPASS_PIPELINE
#import bevy_pbr::{
    prepass_io::{VertexOutput, FragmentOutput},
    pbr_deferred_functions::deferred_output,
}
#else
#import bevy_pbr::{
    forward_io::{VertexOutput, FragmentOutput},
    pbr_functions::{apply_pbr_lighting, main_pass_post_lighting_processing},
    prepass_utils::{prepass_depth, prepass_normal}
}
#endif

struct MyExtendedMaterial {
    quantize_steps: u32,
}

@group(2) @binding(100)
var<uniform> my_extended_material: MyExtendedMaterial;

@fragment
fn fragment(
    in: VertexOutput,
    @builtin(front_facing) is_front: bool,
    #ifdef MULTISAMPLED
    @builtin(sample_index) sample_index: u32,
#endif
) -> FragmentOutput {
    #ifndef MULTISAMPLED
    let sample_index = 0u;
    #endif

    var pbr_input = pbr_input_from_standard_material(in, is_front);
    pbr_input.material.base_color = alpha_discard(pbr_input.material, pbr_input.material.base_color);

#ifdef PREPASS_PIPELINE
    // in deferred mode we can't modify anything after that, as lighting is run in a separate fullscreen shader.
    let out = deferred_output(in, pbr_input);
#else
    var out: FragmentOutput;
    out.color = apply_pbr_lighting(pbr_input);

//    out.color = vec4<f32>(vec4<u32>(out.color * f32(my_extended_material.quantize_steps))) / f32(my_extended_material.quantize_steps);
    let color_oklab = oklab_from_linear(out.color.xyz);
    let quantized_lightness = floor(color_oklab.x * f32(my_extended_material.quantize_steps)) / f32(my_extended_material.quantize_steps);
    out.color = vec4<f32>(linear_from_oklab(vec3<f32>(quantized_lightness, color_oklab.y, color_oklab.z)).xyz, out.color.a);
    out.color = main_pass_post_lighting_processing(pbr_input, out.color);
#endif

    return out;
}

fn vec3_avg(color: vec3f) -> f32 {
	return (color.r + color.g + color.b) / 3.0;
}

//By Björn Ottosson
//https://bottosson.github.io/posts/oklab
//Shader functions adapted by "mattz"
//https://www.shadertoy.com/view/WtccD7

fn oklab_from_linear(linear: vec3f) -> vec3f
{
    let im1: mat3x3<f32> = mat3x3<f32>(0.**********, 0.**********, 0.**********,
                          0.**********, 0.**********, 0.**********,
                          0.**********, 0.**********, 0.**********);

    let im2: mat3x3<f32> = mat3x3<f32>(0.2104542553, 1.9779984951, 0.0259040371,
                          0.7936177850, -2.4285922050, 0.7827717662,
                          -0.0040720468, 0.4505937099, -0.8086757660);

    let lms: vec3f = im1 * linear;

    return im2 * (sign(lms) * pow(abs(lms), vec3(1.0/3.0)));
}

fn linear_from_oklab(oklab: vec3f) -> vec3f
{
    let m1: mat3x3<f32> = mat3x3<f32>(1.000000000, 1.000000000, 1.000000000,
                         0.396337777, -0.105561346, -0.089484178,
                         0.215803757, -0.063854173, -1.291485548);

    let m2: mat3x3<f32> = mat3x3<f32>(4.076724529, -1.268143773, -0.004111989,
                         -3.307216883, 2.609332323, -0.703476310,
                         0.230759054, -0.341134429, 1.706862569);
    let lms: vec3f = m1 * oklab;

    return m2 * (lms * lms * lms);
}

//By Inigo Quilez, under MIT license
//https://www.shadertoy.com/view/ttcyRS
fn oklab_mix(lin1: vec3f, lin2: vec3f, a: f32) -> vec3f
{
    // https://bottosson.github.io/posts/oklab
    let kCONEtoLMS: mat3x3<f32> = mat3x3<f32>(
         0.**********,  0.**********,  0.**********,
         0.**********,  0.**********,  0.**********,
         0.**********,  0.**********,  0.**********);
    let kLMStoCONE: mat3x3<f32> = mat3x3<f32>(
         4.**********, -1.**********, -0.**********,
        -3.**********,  2.**********, -0.**********,
         0.**********, -0.**********,  1.**********);

    // rgb to cone (arg of pow can't be negative)
    let lms1: vec3f = pow( kCONEtoLMS*lin1, vec3(1.0/3.0) );
    let lms2: vec3f = pow( kCONEtoLMS*lin2, vec3(1.0/3.0) );
    // lerp
    var lms: vec3f = mix( lms1, lms2, a );
    // gain in the middle (no oklab anymore, but looks better?)
    lms *= 1.0+0.2*a*(1.0-a);
    // cone to rgb
    return kLMStoCONE*(lms*lms*lms);
}