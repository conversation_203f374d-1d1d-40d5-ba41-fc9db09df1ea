#import bevy_pbr::forward_io::VertexOutput
#import bevy_pbr::{
    prepass_utils::{prepass_depth, prepass_normal}
}

struct ToonShaderMaterial {
    color: vec4<f32>,
    sun_dir: vec3<f32>,
    sun_color: vec4<f32>,
    camera_pos: vec3<f32>,
    ambient_color: vec4<f32>,
};

@group(2) @binding(0)
var<uniform> material: ToonShaderMaterial;
@group(2) @binding(1)
var base_color_texture: texture_2d<f32>;
@group(2) @binding(2)
var base_color_sampler: sampler;

@fragment
fn fragment(in: VertexOutput) -> @location(0) vec4<f32> {
    let base_color = material.color * textureSample(base_color_texture, base_color_sampler, in.uv);
    let normal = normalize(in.world_normal);
    let n_dot_l = dot(material.sun_dir, normal);
    var light_intensity = 0.0;

    if n_dot_l > 0.0 {
        let bands = 1.0;
        var x = n_dot_l * bands;
        x = round(x);
        light_intensity = x / bands;
    } else {
        light_intensity = 0.0;
    }

    let light = light_intensity * material.sun_color;

    let view_dir: vec3<f32> = normalize(material.camera_pos - in.world_position.xyz);

    let half_vector = normalize(material.sun_dir + view_dir);
    let n_dot_h = dot(normal, half_vector);
    let glossiness = 0.0;
    let specular_intensity = pow(n_dot_h, glossiness * glossiness);

    let specular_intensity_smooth = smoothstep(0.005, 0.01, specular_intensity);
    let specular = specular_intensity_smooth * vec4<f32>(0.9, 0.9, 0.9, 1.0);

    // Apply a vertical gradient based on the y coordinate of the world position
    let gradient_factor = clamp((in.world_position.y + 1.0) * 0.2, 0.0, 1.0); // Assuming y is in range [-1, 1]
    let gradient_color = mix(vec4<f32>(0.0, 0.0, 0.0, 1.0), vec4<f32>(1.0, 1.0, 1.0, 1.0), gradient_factor);

    let sample_index = 0u;
    let edge_magnitude = normal_edges(in.position, sample_index);
//    let edge_color = base_color * gradient_color * edge_magnitude * 0.2;
    let edge_color = vec4<f32>(1.0, 1.0, 1.0, 1.0);

    let final_color = base_color * gradient_color * (light + material.ambient_color + specular) * 1.15;

//    return mix(final_color, edge_color, edge_magnitude);
    return mix(final_color, edge_color, edge_magnitude);
}

fn modulate (x: f32, y: f32) -> f32 {
    return x - y * floor(x / y);
}

fn normal_edges(position: vec4f, sample_index: u32) -> f32 {
    let normal = prepass_normal(position, sample_index);

    let neighbour_left = prepass_normal(position, sample_index);
	let neighbour_right = prepass_normal( position + vec4(0.5, 0., 0., 0.), sample_index);

	let neighbour_top = prepass_normal( position, sample_index);
	let neighbour_bottom = prepass_normal( position + vec4(0., 0.5, 0.,0.), sample_index);

    let edge_color = vec3(1.,1.,1.);
    let edge_strength = 1.;

    return 1. - min(
        dot(neighbour_left, neighbour_right),
        dot(neighbour_top, neighbour_bottom)
    );
}