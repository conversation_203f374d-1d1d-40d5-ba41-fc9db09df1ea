// 1) Import Bevy’s PBR/forward pipeline hooks
#import bevy_pbr::{
    mesh_bindings::mesh,
    mesh_functions,
    mesh_view_bindings::globals,
    forward_io::{Vertex},
    view_transformations::position_world_to_clip,
}
#import bevy_render::globals::Globals  // for camera_position

// 2) Uniforms (group 2) from our Rust material
struct GrassParams {
    density_scale: f32,
    wind_strength: f32,
    wind_speed:    f32,
    blade_height:  f32,
    steps:         u32,
};

@group(2) @binding(99)
var<uniform> grass: GrassParams;

// 3) View/camera data (group 1)
//@group(1) @binding(0)
//var<uniform> globals: Globals;

@group(0) @binding(0)
var<uniform> camera_transform: mat4x4<f32>;

// 4) Per-vertex outputs to fragment
struct VertexOutput {
    @builtin(position) position: vec4<f32>,
    @location(0) world_pos:     vec4<f32>,  // world-space position
};

// 5) Simple pseudo-random noise for density
fn pseudo_noise(p: vec3<f32>) -> f32 {
    return fract(sin(dot(p, vec3<f32>(12.9898,78.233,45.164))) * 43758.5453);
}

// 6) Vertex shader: position proxy mesh in world
@vertex
fn vertex(in: Vertex) -> VertexOutput {
    var out: VertexOutput;
    let world_from_local = mesh_functions::get_world_from_local(in.instance_index);
    let world = mesh_functions::mesh_position_local_to_world(
        world_from_local,
        vec4<f32>(in.position, 1.0),
    );
    out.position  = position_world_to_clip(world.xyz);
    out.world_pos = world;
    return out;
}

// 7) Fragment shader: ray-march through bounding volume
@fragment
fn fragment(in: VertexOutput) -> @location(0) vec4<f32> {
    // Ray origin & dir in world‐space
    let ro = vec3<f32>(camera_transform[2][0], camera_transform[2][1], camera_transform[2][2]);
    let rd = normalize(in.world_pos.xyz - ro);

//    let ro: vec4<f32> = vec4<f32>(ro1, 0.0);  // vec4<f32>
//    let diff4: vec4<f32> = in.world_pos - ro;                 // vec4<f32> - vec4<f32>
//    let rd: vec3<f32>    = normalize(diff4.xyz);

    // AABB intersection for unit cube centered at origin
    // (Assuming your mesh is a unit cube scaled in Transform)
    let tMin = (vec3<f32>(-0.5) - ro) / rd;
    let tMax = (vec3<f32>(0.5)  - ro) / rd;
    let t0   = max(max(min(tMin.x, tMax.x), min(tMin.y, tMax.y)),
                   min(tMin.z, tMax.z));
    let t1   = min(min(max(tMin.x, tMax.x), max(tMin.y, tMax.y)),
                   max(tMin.z, tMax.z));
    if (t1 <= max(t0, 0.0)) { discard; }

    // March samples & composite
    let dt = (t1 - t0) / f32(grass.steps);
    var t  = max(t0, 0.0);
    var accum: vec4<f32> = vec4<f32>(0.0);
    for (var i = 0u; i < grass.steps; i = i + 1u) {
        let p = ro + rd * (t + dt * f32(i));
        // height falloff: no grass above blade_height
        let hf = clamp(p.y / grass.blade_height, 0.0, 1.0);
        // density from noise + falloff
        let d = pseudo_noise(p * grass.density_scale) * hf;
        let alpha = d * dt;
        // gradient green based on height
        let col = mix(vec3<f32>(0.1,0.4,0.1), vec3<f32>(0.3,0.8,0.2), hf);
        let delta_rgb = (1.0 - accum.a) * col * alpha;
        let delta_a   = (1.0 - accum.a) * alpha;
        accum = vec4<f32>(
            accum.rgb + delta_rgb,  // new rgb
            accum.a   + delta_a     // new alpha
        );
        if (accum.a > 0.95) { break; }
    }
    return accum;
}
