#import bevy_pbr::{
    mesh_bindings::mesh,
    mesh_functions,
    mesh_view_bindings::globals,
    forward_io::{Vertex},
    view_transformations::position_world_to_clip,
}
#import bevy_render::globals::Globals  // for camera_position

// grass.wgsl
// A combined vertex+fragment WGSL shader for Bevy 0.16

struct MaterialUniform {
    sway: f32,
    sway_: f32,
    sway_pow: f32,
    grass_roughness: f32,
    sway_noise_sampling_scale: f32,
    sway_time_scale: f32,
    sway_dir: vec3<f32>,
    color_scale: f32,
    color_grad_height: f32,
    grass_scale: vec3<f32>,
    top_color: vec4<f32>,
    bot_color: vec4<f32>,
};
@group(1) @binding(0)
var<uniform> material: MaterialUniform;
@group(1) @binding(1) var sway_noise: texture_2d<f32>;
@group(1) @binding(2) var sway_noise_sampler: sampler;

// Built‐in uniforms from Bevy
@group(0) @binding(0) var<uniform> ViewProj: mat4x4<f32>;
@group(0) @binding(1) var<uniform> Model: mat4x4<f32>;
@group(0) @binding(2) var<uniform> Time: f32;

//struct VertexInput {
////    @builtin(vertex_index) index: u32,
//    @location(0) pos: vec3<f32>,
//}

//struct VertexOutput {
//    @builtin(position) clip_pos: vec4<f32>,
//    @location(0) world_y: f32,
//    @location(1) wind: f32,
//    @location(2) normal: vec3<f32>,
////    @builtin(front_facing) front_facing: bool
//};

//@vertex
//fn vertex_main(
//    @builtin(vertex_index) vertex_input: u32
//) -> VertexOutput {
//    var out: VertexOutput;
//
////    // 1) Scale the grass blade
////    var pos = in.pos * material.grass_scale;
////
////    // 2) Compute world‐space position
////    let world_pos4 = Model * vec4<f32>(pos, 1.0);
////    let world_xy = world_pos4.xz;
////
////    // 3) Sample wind noise
////    let dir2 = normalize(material.sway_dir.xz);
////    let noise_uv = dir2 * (-Time * material.sway_time_scale)
////                  + world_xy * material.sway_noise_sampling_scale;
////    let wind_val = textureSample(sway_noise, sway_noise_sampler, noise_uv).r;
////
////    // 4) Clamp and lift the sway
////    let wind_clamped = clamp(((wind_val - 0.5) * 1.5) + 0.5, 0.0, 1.0);
////    let sway_vec_model = (Model * vec4<f32>(material.sway_dir,0.0)).xyz;
////    pos += normalize(sway_vec_model) * material.sway * pos.y * wind_clamped;
////
////    // 5) Vertical curl
////    pos.y -= material.sway_ * abs(pow(pos.x, material.sway_pow));
////
////    // 6) Output
//////    out.clip_pos = ViewProj * Model * vec4<f32>(pos, 1.0);
//////    out.world_y = (Model * vec4<f32>(pos,1.0)).y;
////
////    out.wind = wind_val;
////    out.normal = vec3<f32>(0.0, 1.0, 0.0);
//    return out;
//}

//struct Vertex {
//    @location(0) position: vec3<f32>,
//    @location(1) normal: vec3<f32>,
//    @location(2) uv: vec2<f32>,
//
//    @location(3) i_pos_scale: vec4<f32>,
//    @location(4) i_color: vec4<f32>,
//};

struct VertexOutput {
    @builtin(position) clip_position: vec4<f32>,
    @location(0) color: vec4<f32>,
};

struct FragmentInput {
    @location(0) world_y: f32,
    @location(1) wind: f32,
    @location(2) normal: vec3<f32>,
//    @builtin(front_facing) front_facing: bool
};

//struct FragmentOutput {
//    @location(0) base_color: vec4<f32>,
//    @location(1) roughness: f32,
//    @location(2) normal: vec3<f32>,
//    @location(3) metallic: f32
//};

@vertex
fn vertex(vertex: Vertex) -> VertexOutput {
    var out: VertexOutput;
    return out;
}

@fragment
fn fragment_main(
    in: VertexOutput
) -> @location(0) vec4<f32> {
//    var out: FragmentOutput;

//    // Gradient blend
//    let t = (in.world_y + material.color_grad_height) * material.color_scale;
//    let albedo = mix(material.bot_color.rgb, material.top_color.rgb, t);
//
//    // Roughness from wind
//    let wind_clamped = clamp(((in.wind - 0.5) * 1.5) + 0.5, 0.0, 1.0);
//    let rough = clamp(1.0 - (wind_clamped * material.grass_roughness), 0.2, 1.0);
//
//    out.base_color = vec4<f32>(albedo, 1.0);
//    out.roughness = rough;
//    // Flip normal on back‐faces
////    out.normal = in.normal * select(-1.0, 1.0, in.front_facing);
//    out.metallic = 0.0;
    return in.color;
}
