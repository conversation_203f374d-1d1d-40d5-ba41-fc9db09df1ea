#import bevy_pbr::{
    mesh_bindings::mesh,
    mesh_functions,
    mesh_view_bindings::globals,
    forward_io::{Vertex},
    view_transformations::position_world_to_clip,
}
#import bevy_render::globals::Globals

struct GrassProperties {
    wind_strength: f32,
    wind_speed: f32,
    blade_height: f32,
    blade_width: f32,
}

@group(2) @binding(100)
var<uniform> grass: GrassProperties;

struct VertexOutput {
    @builtin(position) position: vec4<f32>,
    @location(0) world_position: vec4<f32>,
    @location(1) normal: vec3<f32>,
    @location(2) uv: vec2<f32>,
};

// Vertex shader: apply height‐weighted wind sway
@vertex
fn vertex(
    in: Vertex
) -> VertexOutput {
    var out: VertexOutput;

    // 1) Fetch the per-instance world-from-local matrix
    let world_from_local = mesh_functions::get_world_from_local(in.instance_index);

    // 2) Compute basic world position from local
    var world_pos = mesh_functions::mesh_position_local_to_world(
        world_from_local,
        vec4<f32>(in.position, 1.0),
    );

    // 3) Apply wind in local space (height-weighted sine sway)
    let height_factor = clamp(in.position.y / grass.blade_height, 0.0, 1.0);
    let phase = (in.position.x + in.position.z) * 0.5
                + globals.time * grass.wind_speed;
    let sway = sin(phase) * grass.wind_strength * height_factor;
    let swayed_local = vec4<f32>(
        in.position.x + sway,
        in.position.y,
        in.position.z + sway,
        1.0,
    );

    // 4) Recompute world & clip position after sway
    world_pos = mesh_functions::mesh_position_local_to_world(world_from_local, swayed_local);
    out.world_position = world_pos;
    out.position = position_world_to_clip(world_pos.xyz);

    // 5) Normals and UVs
    out.normal = normalize((world_from_local * vec4<f32>(in.normal, 0.0)).xyz);
    out.uv     = in.uv;

    return out;
}

// Fragment shader: green gradient with blade‐width taper
@fragment
fn fragment(
    in: VertexOutput
) -> @location(0) vec4<f32> {
    let height_factor = clamp(in.world_position.y / grass.blade_height, 0.0, 1.0);

    // Vertical gradient: darker at base, lighter at tip
    let base_color = vec3<f32>(0.1, 0.5, 0.1);
    let tip_color  = vec3<f32>(0.3, 0.8, 0.2);
    let color = mix(base_color, tip_color, height_factor);

    // Horizontal taper: blades thinner at edges
    let edge_dist = abs(in.uv.x - 0.5) * 2.0;
    let taper = smoothstep(1.0, 1.0 - grass.blade_width, edge_dist);

    return vec4<f32>(color * taper, 1.0);
}