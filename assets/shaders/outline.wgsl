#import bevy_core_pipeline::fullscreen_vertex_shader::FullscreenVertexOutput
#import bevy_render::view::View

// Resource bindings
@group(0) @binding(0) var screen_texture: texture_2d<f32>;
@group(0) @binding(1) var texture_sampler: sampler;

#ifdef MULTISAMPLED
@group(0) @binding(2) var depth_texture: texture_depth_multisampled_2d;
#else
@group(0) @binding(2) var depth_texture: texture_depth_2d;
#endif

@group(0) @binding(3) var normal_texture: texture_2d<f32>;

// Settings passed from Rust
struct OutlineSettings {
    viewport_size: vec2<f32>,
    shadows_enabled: u32,
    highlights_enabled: u32,
    shadow_strength: f32,
    highlight_strength: f32,
    highlight_color: vec3<f32>,
    shadow_color: vec3<f32>,
    light_position: vec3<f32>,
    light_direction: vec3<f32>,
};

@group(0) @binding(4) var<uniform> settings: OutlineSettings;
@group(0) @binding(5) var<uniform> view: View;

fn get_view_depth(uv: vec2<f32>) -> f32 {
    let raw = textureSample(depth_texture, texture_sampler, uv);
    let ndcZ  = raw * 2.0 - 1.0;
    let clipZ = ndcZ; // w = 1 in ortho
    let zf    = 1000.0;
    let zn    = -1000.0;
    let camZ  = ((clipZ + (zf + zn) / (zf - zn)) * (zf - zn)) / -2.0;
    return camZ;
}

fn get_view_depth3(uv: vec2<f32>) -> f32 {
    let raw = textureSample(depth_texture, texture_sampler, uv);
    let clip_xy = vec2<f32>(uv.x * 2.0 - 1.0, 1.0 - 2.0 * uv.y);
    let t = view.view_from_clip * vec4<f32>(clip_xy, raw, 1.0);
    let view_xyz = t.xyz / t.w;
    return -view_xyz.z;
}

fn get_view_depth2(uv: vec2<f32>) -> f32 {
    // 1) Sample raw depth in [0,1]
    let raw = textureSample(depth_texture, texture_sampler, uv);

    // 2) Remap to NDC z
    let ndc_z = raw * 2.0 - 1.0;

    // 3) Build clip-space xy from uv
    let clip_xy = uv * 2.0 - 1.0;

    // 4) Assemble homogeneous clip-space position
    let clip_pos = vec4<f32>(clip_xy, ndc_z, 1.0);

    // 5) Transform into view space (homogeneous)
    let view_pos_h = view.view_from_clip * clip_pos;

    // 6) Perspective divide (restore 3D position)
    let view_pos = view_pos_h.xyz / view_pos_h.w;

    // 7) Return view-space Z (negative if you need positive-forward)
    return -view_pos.z;
}

fn get_view_pos(uv: vec2<f32>) -> vec3<f32> {
    let raw = textureSample(depth_texture, texture_sampler, uv);
    let ndc_z = raw * 2.0 - 1.0;
    let clip = vec4<f32>(uv * 2.0 - 1.0, ndc_z, 1.0);
    let view_h = view.view_from_clip * clip;
    return view_h.xyz / view_h.w;
}

// Compute a normal-based edge indicator
fn normal_indicator(base: vec3<f32>, sample: vec3<f32>, depth_diff: f32) -> f32 {
    let bias = vec3<f32>(1.0);
    let diff = dot(base - sample, bias);
    let n_ind = clamp(smoothstep(-0.01, 0.01, diff), 0.0, 1.0);
    let d_ind = clamp(sign(depth_diff * 0.25 + 0.0025), 0.0, 1.0);
    return (1.0 - dot(base, sample)) * d_ind * n_ind;
}

@fragment
fn fragment(in: FullscreenVertexOutput) -> @location(0) vec4<f32> {
    let e = vec2<f32>(1.0) / settings.viewport_size;

    // Depth-based outline
    let depth = get_view_depth(in.uv);
    var depth_diff = 0.0;
    if (settings.shadows_enabled == 1u) {
        let du = get_view_depth(in.uv + vec2<f32>(0.0, -1.0) * e);
        let dr = get_view_depth(in.uv + vec2<f32>(1.0,  0.0) * e);
        let dd = get_view_depth(in.uv + vec2<f32>(0.0,  1.0) * e);
        let dl = get_view_depth(in.uv + vec2<f32>(-1.0, 0.0) * e);
        depth_diff += clamp(du - depth, 0.0, 1.0);
        depth_diff += clamp(dd - depth, 0.0, 1.0);
        depth_diff += clamp(dr - depth, 0.0, 1.0);
        depth_diff += clamp(dl - depth, 0.0, 1.0);
        depth_diff = smoothstep(0.2, 0.3, depth_diff);
    }

    // Normal-based highlights
    var normal_diff = 0.0;
    var neg_depth_diff = .5;
    if (settings.highlights_enabled == 1u) {
        let n0 = textureSample(normal_texture, texture_sampler, in.uv).rgb * 2.0 - 1.0;
        let nu = textureSample(normal_texture, texture_sampler, in.uv + vec2<f32>(0.0,-1.0)*e).rgb * 2.0 - 1.0;
        let nr = textureSample(normal_texture, texture_sampler, in.uv + vec2<f32>(1.0, 0.0)*e).rgb * 2.0 - 1.0;
        let nd = textureSample(normal_texture, texture_sampler, in.uv + vec2<f32>(0.0, 1.0)*e).rgb * 2.0 - 1.0;
        let nl = textureSample(normal_texture, texture_sampler, in.uv + vec2<f32>(-1.0,0.0)*e).rgb * 2.0 - 1.0;
        normal_diff += normal_indicator(n0, nu, depth_diff);
        normal_diff += normal_indicator(n0, nr, depth_diff);
        normal_diff += normal_indicator(n0, nd, depth_diff);
        normal_diff += normal_indicator(n0, nl, depth_diff);
        normal_diff = smoothstep(0.2, 0.8, normal_diff);
        normal_diff = clamp(normal_diff - depth_diff, 0.0, 1.0);
    }

    // Compose final color
    let base = textureSample(screen_texture, texture_sampler, in.uv).rgb;
    var result = base;

    // Highlight edges _and_ weight by how much the pixel “sees” the light position
    if (settings.highlights_enabled == 1u) {
        let hl = mix(base, settings.highlight_color, settings.highlight_strength);

        // view‐space normal
        let n0 = textureSample(normal_texture, texture_sampler, in.uv).rgb * 2.0 - 1.0;
        // view‐space pixel position
        let view_pos = get_view_pos(in.uv);
        // view‐space light position
        let light_vs = (view.view_from_clip * vec4<f32>(settings.light_position, 1.0)).xyz;
        // lambertian toward the light position
        let lam_pos = max(dot(normalize(n0), normalize(light_vs - view_pos)), 0.0);

        result = mix(result, hl, normal_diff * lam_pos);
    }

    // Shadow edges _and_ weight by how much the pixel faces _away_ from the light direction
    if (settings.shadows_enabled == 1u) {
        let sh = mix(base, settings.shadow_color, settings.shadow_strength);

        // view‐space normal
        let n0 = textureSample(normal_texture, texture_sampler, in.uv).rgb * 2.0 - 1.0;
        // view‐space light direction (w=0 for direction vectors)
        let light_dir_vs = normalize((view.view_from_clip * vec4<f32>(settings.light_direction, 0.0)).xyz);
        // lambertian of the _infinite_ light direction
        let lam_dir = max(dot(normalize(n0), -light_dir_vs), 0.0);
        // invert for shadows: surfaces facing away get stronger darkening
        let shadow_weight = depth_diff * (1.0 - lam_dir);

        result = mix(result, sh, shadow_weight);
    }
//    if (settings.highlights_enabled == 1u) {
//        let hl = mix(base, settings.highlight_color, settings.highlight_strength);
//        result = mix(result, hl, normal_diff);
//    }

//    if (settings.shadows_enabled == 1u) {
//        let sh = mix(base, settings.shadow_color, settings.shadow_strength);
//        result = mix(result, sh, depth_diff);
//    }

    return vec4<f32>(result, 1.0);
}