// Heavily adapted from https://github.com/tbillington/bevy_toon_shader
// And https://roystan.net/articles/toon-shader/
// Pull in Bevy’s camera uniform definition
//#define_import_path bevy_render::view
#import bevy_render::view::View

// VertexOutput from Bevy’s forward PBR pipeline
#import bevy_pbr::forward_io::VertexOutput

@group(1) @binding(0)
var<uniform> view: View;

struct ToonMaterial {
    base_color: vec4<f32>,
    light_direction: vec3<f32>,
    light_color: vec4<f32>,
    camera_position: vec3<f32>,
    ambient_color: vec4<f32>,
    rim_amount: f32,
    rim_color: vec4<f32>,
    rim_threshold: f32,
    band_count: u32,
    light_space: mat4x4<f32>,
};

@group(2) @binding(0) var<uniform> material: ToonMaterial;
@group(2) @binding(1) var albedo_map: texture_2d<f32>;
@group(2) @binding(2) var albedo_sampler: sampler;
@group(2) @binding(3) var shadow_map: texture_depth_2d;
@group(2) @binding(4) var shadow_sampler: sampler_comparison;

// ----------------------------------------------------------------------------
//  Helper: perform a PCF shadow lookup
// ----------------------------------------------------------------------------
fn get_shadow_pcf(world_pos: vec3<f32>) -> f32 {
    // 1) Transform world position into light clip space
    let light_clip: vec4<f32> = material.light_space * vec4<f32>(world_pos, 1.0);
    // 2) Perspective divide → NDC
    let ndc: vec3<f32> = light_clip.xyz / light_clip.w;
    // 3) Map NDC xy to [0,1] albedo_map UV
    let uv: vec2<f32> = ndc.xy * 0.5 + vec2<f32>(0.5, 0.5);
    // 4) Depth bias to reduce self-shadowing
    let bias: f32 = 0.005;
    // 5) Gather four PCF comparisons around UV and average
    let cmp: vec4<f32> = textureGatherCompare(
        shadow_map,
        shadow_sampler,
        uv,
        ndc.z - bias
    );
    return (cmp.x + cmp.y + cmp.z + cmp.w) * 0.25;
}

fn get_shadow( world_pos: vec3<f32> ) -> f32 {
    let clip = material.light_space * vec4<f32>(world_pos, 1.0);
    let ndc  = clip.xyz / clip.w;
    let uv   = ndc.xy * 0.5 + vec2<f32>(0.5);
    let bias = 0.005;
    return textureSampleCompare(shadow_map, shadow_sampler, uv, ndc.z - bias);  // :contentReference[oaicite:13]{index=13}
}

// ----------------------------------------------------------------------------
//  Main fragment entry point
// ----------------------------------------------------------------------------
@fragment
fn fragment(input: VertexOutput) -> @location(0) vec4<f32> {
    // --- UVs and base color ---
    #ifdef VERTEX_UVS_A
        let uv: vec2<f32> = input.uv;
    #else
        let uv: vec2<f32> = vec2<f32>(0.0, 0.0);
    #endif

    let albedo: vec4<f32> = material.base_color
        * textureSample(albedo_map, albedo_sampler, uv);

    // --- Surface normal and view direction ---
    let normal: vec3<f32> = normalize(input.world_normal);
    let view_dir: vec3<f32> = normalize(material.camera_position - input.world_position.xyz);

    // --- Diffuse (banded or smooth) ---
    let n_dot_l: f32 = dot(material.light_direction, normal);
    var diff: f32 = 0.0;
    if (material.band_count > 0u) {
        if (n_dot_l > 0.0) {
            let bands: f32 = f32(material.band_count);
            let stepped: f32 = round(n_dot_l * bands) / bands;
            diff = stepped;
        }
    } else {
        diff = smoothstep(0.0, 0.01, n_dot_l);
    }

    // --- Shadow factor ---
    let shadow: f32 = get_shadow_pcf(input.world_position.xyz);
    // Darken the diffuse if in shadow (keep some ambient)
    let lit: f32 = mix(0.4, 1.0, shadow);   // 0.4 ambient in full shadow

    let diffuse: vec4<f32> = diff * material.light_color * lit;

    // --- Specular ---
    let half_vec: vec3<f32> = normalize(material.light_direction + view_dir);
    let n_dot_h: f32 = max(dot(normal, half_vec), 0.0);
    let spec_raw: f32 = pow(n_dot_h * diff, 32.0 * 32.0);
    let spec_smooth: f32 = smoothstep(0.005, 0.01, spec_raw);
    let spec_color: vec4<f32> = vec4<f32>(0.9, 0.9, 0.9, 1.0);
    let specular: vec4<f32> = spec_smooth * spec_color * lit;

    // --- Rim lighting ---
    let rim_dot: f32 = 1.0 - dot(view_dir, normal);
    var rim_int: f32 = rim_dot * pow(n_dot_l, material.rim_threshold);
    rim_int = smoothstep(
        material.rim_amount - 0.01,
        material.rim_amount + 0.01,
        rim_int
    );
    let rim: vec4<f32> = rim_int * material.rim_color * lit;

    // --- Ambient (always on) ---
    let ambient: vec4<f32> = material.ambient_color;

    // --- Compose final color ---
    let color: vec4<f32> = albedo * (ambient + diffuse + specular + rim);
    return vec4<f32>(color.rgb, albedo.a);
}

//@fragment
//fn fragment(input: VertexOutput) -> @location(0) vec4<f32> {
//
//    #ifdef VERTEX_UVS_A
//        let uv = input.uv;
//    #else
//        let uv = vec2(1.0, 1.0);
//    #endif
//
//    let base_color = material.base_color * textureSample(albedo_map, albedo_sampler, uv);
//
//    // shading the object
//    let normal = normalize(input.world_normal); // make the world_normal of the input mesh have a length of one
//    let n_dot_l = dot(material.light_direction, normal) ;
//    var light_intensity = 0.0;
//
//    // if we want bands, create bands, otherwise use smooth lighting
//    if material.band_count > 0 {
//        if n_dot_l > 0.0 {
//            var x = round(n_dot_l * f32(material.band_count));
//            light_intensity = x / f32(material.band_count);
//        } else {
//            light_intensity = 0.0;
//        }
//    } else {
//        light_intensity = smoothstep(0.0, 0.01, n_dot_l);
//    }
//
//    let light = (light_intensity * material.light_color);
//
//    // specular
//    let view_direction = normalize(material.camera_position - input.world_position.xyz);
//    let half_vector = normalize(material.light_direction + view_direction);
//    let n_dot_h = dot(normal, half_vector);
//    let spec_color = vec4<f32>(0.9, 0.9, 0.9, 1.0);
//    let glossiness = 32.0;
//    let spec_intensity = pow(n_dot_h * light_intensity, glossiness * glossiness);
//    let spec_intensity_smooth = smoothstep(0.005, 0.01, spec_intensity);
//    let specular = spec_intensity_smooth * spec_color;
//
//    // rim lighting
//    let rim_dot = 1 - dot(view_direction, normal);
//    var rim_intensity = rim_dot * pow(n_dot_l, material.rim_threshold);
//    rim_intensity = smoothstep(material.rim_amount - 0.01, material.rim_amount + 0.01, rim_intensity);
//    let rim = rim_intensity * material.rim_color;
//
//    return base_color * (material.ambient_color + light + specular + rim);
//}